# 万能多开器开发文档

## 项目概述

万能多开器是一个基于C++和Win32 API开发的高级工具，用于自动识别和绕过各种软件的多开限制机制。支持Win32/Win64应用程序，兼容Windows 7-11系统，主要用于测试自己开发的软件的多开防护效果。

### 核心特性
- **通用兼容性** - 支持.NET Framework、Qt、MFC、WinUI等框架的应用程序
- **智能检测** - 基于机器学习和规则引擎的限制类型识别，支持加壳、混淆、虚拟化保护软件
- **用户友好** - 现代化GUI界面，支持文件选择对话框和拖拽操作，实时进度显示和日志输出
- **高级隐蔽** - 反检测技术，绕过各种保护机制，包括反调试检测和虚拟环境检测
- **高性能** - 低内存占用，快速响应，支持批量处理多个软件
- **自适应系统** - 智能检测结果置信度评估和fallback机制
- **向后兼容** - 完整支持Windows 7到Windows 11的所有版本

### 技术优势
- **多框架支持**: 针对.NET Framework、Qt、MFC、WinUI等不同框架提供专门的适配方案
- **机器学习增强**: 集成机器学习算法提高检测准确率和绕过成功率
- **反检测技术**: 包含DLL隐藏、Hook链隐藏、内存清理等高级隐蔽技术
- **虚拟环境适配**: 支持VMware、VirtualBox等虚拟环境的检测和应对
- **商业软件兼容**: 针对Office、Adobe系列等常见商业软件提供专门的绕过策略

## 技术架构

### 完整模块设计

```
UniversalMultiLauncher/
├── src/
│   ├── core/                              # 核心引擎
│   │   ├── UniversalMultiLauncher.h/cpp    # 主控制器
│   │   ├── RestrictionDetector.h/cpp       # 智能检测器
│   │   ├── BypassEngine.h/cpp              # 绕过引擎
│   │   ├── MLClassifier.h/cpp              # 机器学习分类器
│   │   └── RuleEngine.h/cpp                # 规则引擎
│   ├── gui/                               # 图形界面
│   │   ├── MainWindow.h/cpp                # 主窗口
│   │   ├── ProgressDialog.h/cpp            # 进度对话框
│   │   ├── LogViewer.h/cpp                 # 日志查看器
│   │   ├── SettingsDialog.h/cpp            # 设置对话框
│   │   └── DragDropHandler.h/cpp           # 拖拽处理器
│   ├── hooks/                             # Hook模块
│   │   ├── mutex_hook.cpp                  # Mutex Hook DLL
│   │   ├── file_hook.cpp                   # 文件 Hook DLL
│   │   ├── registry_hook.cpp               # 注册表 Hook DLL
│   │   ├── window_hook.cpp                 # 窗口 Hook DLL
│   │   ├── process_hook.cpp                # 进程 Hook DLL
│   │   ├── memory_hook.cpp                 # 内存 Hook DLL
│   │   └── stealth_hook.cpp                # 隐蔽 Hook DLL
│   ├── utils/                             # 工具模块
│   │   ├── PEAnalyzer.h/cpp               # PE文件分析
│   │   ├── ProcessInjector.h/cpp          # 进程注入
│   │   ├── APIMonitor.h/cpp               # API监控
│   │   ├── AntiDebug.h/cpp                # 反调试检测
│   │   ├── VMDetector.h/cpp               # 虚拟机检测
│   │   ├── MemoryManager.h/cpp            # 内存管理
│   │   └── CryptoUtils.h/cpp              # 加密工具
│   ├── detection/                         # 检测模块
│   │   ├── StaticAnalyzer.h/cpp           # 静态分析
│   │   ├── DynamicAnalyzer.h/cpp          # 动态分析
│   │   ├── BehaviorAnalyzer.h/cpp         # 行为分析
│   │   ├── SignatureDB.h/cpp              # 特征数据库
│   │   └── ConfidenceEvaluator.h/cpp      # 置信度评估
│   ├── bypass/                            # 绕过策略
│   │   ├── MutexBypass.h/cpp              # Mutex绕过
│   │   ├── FileBypass.h/cpp               # 文件绕过
│   │   ├── RegistryBypass.h/cpp           # 注册表绕过
│   │   ├── ProcessBypass.h/cpp            # 进程绕过
│   │   ├── WindowBypass.h/cpp             # 窗口绕过
│   │   └── NetworkBypass.h/cpp            # 网络绕过
│   ├── stealth/                           # 隐蔽技术
│   │   ├── DLLHiding.h/cpp                # DLL隐藏
│   │   ├── HookChainHiding.h/cpp          # Hook链隐藏
│   │   ├── MemoryCleaner.h/cpp            # 内存清理
│   │   ├── AntiAntiDebug.h/cpp            # 反反调试
│   │   └── VMEvasion.h/cpp                # 虚拟机逃逸
│   └── main.cpp                           # 程序入口
├── include/                               # 头文件
├── libs/                                  # 第三方库
│   ├── detours/                           # Microsoft Detours
│   ├── json/                              # JSON解析库
│   ├── sqlite/                            # SQLite数据库
│   └── ml/                                # 机器学习库
├── resources/                             # 资源文件
│   ├── icons/                             # 图标
│   ├── configs/                           # 配置文件
│   └── signatures/                        # 特征库
├── docs/                                  # 文档
├── tests/                                 # 测试程序
└── build/                                 # 编译输出
```

### 类关系图

```
UniversalMultiLauncher (主控制器)
├── MainWindow (GUI主窗口)
│   ├── DragDropHandler (拖拽处理)
│   ├── ProgressDialog (进度显示)
│   └── LogViewer (日志查看)
├── RestrictionDetector (检测器)
│   ├── StaticAnalyzer (静态分析)
│   ├── DynamicAnalyzer (动态分析)
│   ├── MLClassifier (机器学习)
│   └── ConfidenceEvaluator (置信度)
├── BypassEngine (绕过引擎)
│   ├── MutexBypass (Mutex绕过)
│   ├── FileBypass (文件绕过)
│   ├── RegistryBypass (注册表绕过)
│   └── ProcessBypass (进程绕过)
└── StealthManager (隐蔽管理)
    ├── DLLHiding (DLL隐藏)
    ├── MemoryCleaner (内存清理)
    └── AntiAntiDebug (反反调试)
```

## 开发环境配置

### 必需工具

1. **Visual Studio 2019/2022** 或 **MinGW-w64**
2. **Windows SDK 10.0** 或更高版本
3. **Microsoft Detours 4.0.1** - API Hook库
4. **SQLite 3.x** - 数据库支持
5. **nlohmann/json** - JSON解析
6. **Git** - 版本控制

### 依赖库安装

#### 1. Microsoft Detours
```bash
# 下载并编译Detours
git clone https://github.com/Microsoft/Detours.git
cd Detours
nmake
```

#### 2. SQLite集成
```bash
# 下载SQLite源码
wget https://www.sqlite.org/2023/sqlite-amalgamation-3420000.zip
unzip sqlite-amalgamation-3420000.zip
```

#### 3. JSON库集成
```bash
# 使用vcpkg安装
vcpkg install nlohmann-json:x64-windows
```

#### 4. 完整项目配置
```cmake
# CMakeLists.txt
cmake_minimum_required(VERSION 3.16)
project(UniversalMultiLauncher)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 包含目录
include_directories(include)
include_directories(libs/detours/include)
include_directories(libs/sqlite)
include_directories(libs/json/include)

# 链接库目录
link_directories(libs/detours/lib.X64)

# 主程序源文件
set(CORE_SOURCES
    src/main.cpp
    src/core/UniversalMultiLauncher.cpp
    src/core/RestrictionDetector.cpp
    src/core/BypassEngine.cpp
    src/core/MLClassifier.cpp
    src/core/RuleEngine.cpp
)

set(GUI_SOURCES
    src/gui/MainWindow.cpp
    src/gui/ProgressDialog.cpp
    src/gui/LogViewer.cpp
    src/gui/SettingsDialog.cpp
    src/gui/DragDropHandler.cpp
)

set(UTILS_SOURCES
    src/utils/PEAnalyzer.cpp
    src/utils/ProcessInjector.cpp
    src/utils/APIMonitor.cpp
    src/utils/AntiDebug.cpp
    src/utils/VMDetector.cpp
    src/utils/MemoryManager.cpp
    src/utils/CryptoUtils.cpp
)

set(DETECTION_SOURCES
    src/detection/StaticAnalyzer.cpp
    src/detection/DynamicAnalyzer.cpp
    src/detection/BehaviorAnalyzer.cpp
    src/detection/SignatureDB.cpp
    src/detection/ConfidenceEvaluator.cpp
)

set(BYPASS_SOURCES
    src/bypass/MutexBypass.cpp
    src/bypass/FileBypass.cpp
    src/bypass/RegistryBypass.cpp
    src/bypass/ProcessBypass.cpp
    src/bypass/WindowBypass.cpp
    src/bypass/NetworkBypass.cpp
)

set(STEALTH_SOURCES
    src/stealth/DLLHiding.cpp
    src/stealth/HookChainHiding.cpp
    src/stealth/MemoryCleaner.cpp
    src/stealth/AntiAntiDebug.cpp
    src/stealth/VMEvasion.cpp
)

# SQLite源文件
set(SQLITE_SOURCES
    libs/sqlite/sqlite3.c
)

# 主可执行文件
add_executable(MultiLauncher
    ${CORE_SOURCES}
    ${GUI_SOURCES}
    ${UTILS_SOURCES}
    ${DETECTION_SOURCES}
    ${BYPASS_SOURCES}
    ${STEALTH_SOURCES}
    ${SQLITE_SOURCES}
)

# 链接库
target_link_libraries(MultiLauncher
    detours
    psapi
    advapi32
    kernel32
    user32
    shell32
    ole32
    oleaut32
    uuid
    comctl32
    gdi32
    comdlg32
    shlwapi
    wininet
    ws2_32
    dbghelp
    version
)

# Hook DLL列表
set(HOOK_DLLS
    mutex_hook
    file_hook
    registry_hook
    window_hook
    process_hook
    memory_hook
    stealth_hook
)

# 编译所有Hook DLL
foreach(HOOK_DLL ${HOOK_DLLS})
    add_library(${HOOK_DLL} SHARED src/hooks/${HOOK_DLL}.cpp)
    target_link_libraries(${HOOK_DLL} detours kernel32 user32 advapi32)
    set_target_properties(${HOOK_DLL} PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/hooks
    )
endforeach()

# 资源文件
if(WIN32)
    target_sources(MultiLauncher PRIVATE resources/app.rc)
endif()

# 编译选项
if(MSVC)
    target_compile_options(MultiLauncher PRIVATE /W4 /WX)
else()
    target_compile_options(MultiLauncher PRIVATE -Wall -Wextra -Werror)
endif()

# 调试信息
set_target_properties(MultiLauncher PROPERTIES
    DEBUG_POSTFIX "_d"
    RELEASE_POSTFIX ""
)
```

## 用户友好的GUI界面设计

### 界面设计原则
- **直观易用**: 采用标准Windows界面风格，符合用户操作习惯
- **实时反馈**: 提供详细的进度显示、状态更新和日志输出
- **多种输入方式**: 支持文件选择对话框和拖拽操作两种软件选择方式
- **响应式布局**: 支持窗口大小调整，控件自适应布局
- **无障碍访问**: 支持键盘导航和屏幕阅读器

### 主窗口布局设计

#### 窗口结构
```
┌─────────────────────────────────────────────────────────────┐
│ 万能多开器 v1.0                                    [_][□][×] │
├─────────────────────────────────────────────────────────────┤
│ 文件 编辑 工具 帮助                                          │
├─────────────────────────────────────────────────────────────┤
│ 目标程序: [C:\Program Files\App\app.exe    ] [浏览...] [分析] │
│ 实例数量: [3        ▲▼] [启动多开]                           │
├─────────────────────────────────────────────────────────────┤
│ ┌─检测结果─────────────────┐ ┌─操作日志─────────────────┐    │
│ │ ✓ Mutex限制 (置信度:95%) │ │ [INFO] 开始分析程序...   │    │
│ │ ✓ 文件锁定 (置信度:87%)  │ │ [INFO] 检测到Mutex限制   │    │
│ │ ✗ 注册表锁 (置信度:12%)  │ │ [WARN] 发现反调试代码    │    │
│ │ ✓ 窗口检测 (置信度:78%)  │ │ [INFO] 分析完成          │    │
│ └─────────────────────────┘ └─────────────────────────┘    │
├─────────────────────────────────────────────────────────────┤
│ 进度: ████████████████████████████████████████ 100%        │
│ 状态: 就绪 - 拖拽文件到此处或点击浏览按钮选择程序             │
└─────────────────────────────────────────────────────────────┘
```

### 主窗口实现

```cpp
#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <windows.h>
#include <commctrl.h>
#include <commdlg.h>
#include <shellapi.h>
#include <string>
#include <vector>
#include <memory>

class DragDropHandler;
class ProgressDialog;
class LogViewer;

class MainWindow {
private:
    HWND hWnd;                          // 主窗口句柄
    HWND hFileEdit;                     // 文件路径编辑框
    HWND hBrowseBtn;                    // 浏览按钮
    HWND hInstanceSpin;                 // 实例数量调节器
    HWND hLaunchBtn;                    // 启动按钮
    HWND hAnalyzeBtn;                   // 分析按钮
    HWND hStatusBar;                    // 状态栏
    HWND hProgressBar;                  // 进度条
    HWND hLogList;                      // 日志列表
    HWND hDetectionList;                // 检测结果列表
    HWND hMenuBar;                      // 菜单栏
    HWND hToolBar;                      // 工具栏
    HWND hSplitter;                     // 分割条

    std::unique_ptr<DragDropHandler> dragDropHandler;
    std::unique_ptr<ProgressDialog> progressDialog;
    std::unique_ptr<LogViewer> logViewer;

    HFONT hFont;                        // 界面字体
    HFONT hBoldFont;                    // 粗体字体
    HICON hIcon;                        // 应用图标
    HICON hSmallIcon;                   // 小图标
    HBRUSH hBackgroundBrush;            // 背景画刷

    bool isAnalyzing;                   // 分析状态标志
    bool isLaunching;                   // 启动状态标志
    bool isDarkMode;                    // 深色模式标志

    // 窗口尺寸和位置
    int windowWidth;
    int windowHeight;
    int minWindowWidth;
    int minWindowHeight;

public:
    MainWindow();
    ~MainWindow();

    bool Initialize(HINSTANCE hInstance);
    void Show(int nCmdShow);
    void UpdateStatus(const std::string& message);
    void UpdateProgress(int percentage);
    void AddLogEntry(const std::string& message, int level = 0);
    void UpdateDetectionResults(const std::vector<std::string>& results);
    void SetDarkMode(bool enabled);

    // 事件处理
    void OnBrowseFile();
    void OnDragDrop(const std::vector<std::string>& files);
    void OnAnalyze();
    void OnLaunch();
    void OnSettings();
    void OnAbout();
    void OnResize(int width, int height);
    void OnMenuCommand(int menuId);

    static LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam);

private:
    void CreateControls();
    void CreateMenuBar();
    void CreateToolBar();
    void SetupLayout();
    void SetupDragDrop();
    void LoadSettings();
    void SaveSettings();
    bool ValidateInput();
    void EnableControls(bool enabled);
    void UpdateTheme();
    void ResizeControls(int width, int height);
};

#endif
```

### 文件选择对话框实现

#### GetOpenFileName API集成
```cpp
// 标准文件选择对话框的完整实现
bool MainWindow::OnBrowseFile() {
    OPENFILENAMEA ofn;
    char szFile[MAX_PATH] = {0};

    // 初始化OPENFILENAME结构
    ZeroMemory(&ofn, sizeof(ofn));
    ofn.lStructSize = sizeof(ofn);
    ofn.hwndOwner = hWnd;
    ofn.lpstrFile = szFile;
    ofn.nMaxFile = sizeof(szFile);

    // 设置文件过滤器 - 支持多种可执行文件格式
    ofn.lpstrFilter = "可执行文件 (*.exe)\0*.exe\0"
                      "所有文件 (*.*)\0*.*\0"
                      "批处理文件 (*.bat)\0*.bat\0"
                      "命令文件 (*.cmd)\0*.cmd\0"
                      "脚本文件 (*.vbs;*.js)\0*.vbs;*.js\0";
    ofn.nFilterIndex = 1;

    // 设置初始目录
    ofn.lpstrInitialDir = "C:\\Program Files";

    // 设置对话框标题和选项
    ofn.lpstrTitle = "选择要多开的程序";
    ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST | OFN_HIDEREADONLY |
                OFN_EXPLORER | OFN_ENABLESIZING | OFN_DONTADDTORECENT;

    // 显示对话框
    if (GetOpenFileNameA(&ofn)) {
        // 更新文件路径编辑框
        SetWindowTextA(hFileEdit, szFile);

        // 记录用户选择的文件
        AddLogEntry("用户选择文件: " + std::string(szFile), 0);

        // 自动开始分析
        if (MessageBoxA(hWnd, "是否立即分析选择的程序？", "确认",
                       MB_YESNO | MB_ICONQUESTION) == IDYES) {
            OnAnalyze();
        }

        return true;
    }

    // 处理用户取消或错误
    DWORD error = CommDlgExtendedError();
    if (error != 0) {
        char errorMsg[256];
        sprintf_s(errorMsg, "文件选择对话框错误: 0x%08X", error);
        AddLogEntry(errorMsg, 2); // 错误级别
    }

    return false;
}
```

### 拖拽功能完整实现

#### DragDropHandler类设计
```cpp
// DragDropHandler.h - 拖拽处理器头文件
#ifndef DRAGDROPHANDLER_H
#define DRAGDROPHANDLER_H

#include <windows.h>
#include <shellapi.h>
#include <string>
#include <vector>
#include <functional>

class DragDropHandler {
private:
    HWND hTargetWnd;                    // 目标窗口句柄
    bool isDragActive;                  // 拖拽激活状态
    HCURSOR hDropCursor;                // 拖拽光标
    HCURSOR hNormalCursor;              // 普通光标

    // 回调函数类型定义
    using DropCallback = std::function<void(const std::vector<std::string>&)>;
    using DragEnterCallback = std::function<void()>;
    using DragLeaveCallback = std::function<void()>;

    DropCallback onDropCallback;        // 文件拖放回调
    DragEnterCallback onDragEnterCallback;  // 拖拽进入回调
    DragLeaveCallback onDragLeaveCallback;  // 拖拽离开回调

public:
    DragDropHandler(HWND hWnd);
    ~DragDropHandler();

    // 初始化拖拽支持
    bool Initialize();

    // 设置回调函数
    void SetDropCallback(DropCallback callback);
    void SetDragEnterCallback(DragEnterCallback callback);
    void SetDragLeaveCallback(DragLeaveCallback callback);

    // 处理拖拽消息
    void HandleDragEnter();
    void HandleDragLeave();
    void HandleDragOver(POINT pt);
    void HandleDrop(HDROP hDrop);

    // 验证拖拽文件
    bool ValidateDroppedFiles(const std::vector<std::string>& files);

    // 获取拖拽状态
    bool IsDragActive() const { return isDragActive; }

private:
    // 提取拖拽文件列表
    std::vector<std::string> ExtractFileList(HDROP hDrop);

    // 检查文件类型
    bool IsExecutableFile(const std::string& filePath);
    bool IsSupportedFile(const std::string& filePath);

    // 更新拖拽视觉效果
    void UpdateDragVisualEffect(bool entering);
};

#endif
```

#### 拖拽功能实现代码
```cpp
// DragDropHandler.cpp - 拖拽处理器实现
#include "DragDropHandler.h"
#include <shlwapi.h>
#include <algorithm>

DragDropHandler::DragDropHandler(HWND hWnd)
    : hTargetWnd(hWnd), isDragActive(false) {
    // 加载拖拽光标
    hDropCursor = LoadCursor(NULL, IDC_HAND);
    hNormalCursor = LoadCursor(NULL, IDC_ARROW);
}

DragDropHandler::~DragDropHandler() {
    // 清理资源
    if (hTargetWnd) {
        DragAcceptFiles(hTargetWnd, FALSE);
    }
}

bool DragDropHandler::Initialize() {
    if (!hTargetWnd) {
        return false;
    }

    // 启用拖拽文件支持
    DragAcceptFiles(hTargetWnd, TRUE);

    return true;
}

void DragDropHandler::SetDropCallback(DropCallback callback) {
    onDropCallback = callback;
}

void DragDropHandler::SetDragEnterCallback(DragEnterCallback callback) {
    onDragEnterCallback = callback;
}

void DragDropHandler::SetDragLeaveCallback(DragLeaveCallback callback) {
    onDragLeaveCallback = callback;
}

void DragDropHandler::HandleDragEnter() {
    isDragActive = true;
    UpdateDragVisualEffect(true);

    // 调用拖拽进入回调
    if (onDragEnterCallback) {
        onDragEnterCallback();
    }
}

void DragDropHandler::HandleDragLeave() {
    isDragActive = false;
    UpdateDragVisualEffect(false);

    // 调用拖拽离开回调
    if (onDragLeaveCallback) {
        onDragLeaveCallback();
    }
}

void DragDropHandler::HandleDragOver(POINT pt) {
    // 更新光标位置和样式
    SetCursor(hDropCursor);

    // 可以在这里添加拖拽悬停效果
    // 例如高亮显示拖拽目标区域
}

void DragDropHandler::HandleDrop(HDROP hDrop) {
    // 提取拖拽的文件列表
    std::vector<std::string> files = ExtractFileList(hDrop);

    // 验证文件
    if (ValidateDroppedFiles(files)) {
        // 调用文件拖放回调
        if (onDropCallback) {
            onDropCallback(files);
        }
    }

    // 清理拖拽状态
    isDragActive = false;
    UpdateDragVisualEffect(false);

    // 释放拖拽句柄
    DragFinish(hDrop);
}

std::vector<std::string> DragDropHandler::ExtractFileList(HDROP hDrop) {
    std::vector<std::string> files;

    // 获取拖拽文件数量
    UINT fileCount = DragQueryFileA(hDrop, 0xFFFFFFFF, NULL, 0);

    for (UINT i = 0; i < fileCount; i++) {
        // 获取文件路径长度
        UINT pathLength = DragQueryFileA(hDrop, i, NULL, 0);

        if (pathLength > 0) {
            // 分配缓冲区并获取文件路径
            std::string filePath(pathLength, '\0');
            DragQueryFileA(hDrop, i, &filePath[0], pathLength + 1);

            // 移除字符串末尾的空字符
            filePath.resize(pathLength);

            files.push_back(filePath);
        }
    }

    return files;
}

bool DragDropHandler::ValidateDroppedFiles(const std::vector<std::string>& files) {
    if (files.empty()) {
        return false;
    }

    // 检查每个文件
    for (const auto& file : files) {
        if (!IsSupportedFile(file)) {
            // 显示错误消息
            std::string errorMsg = "不支持的文件类型: " + file;
            MessageBoxA(hTargetWnd, errorMsg.c_str(), "错误", MB_OK | MB_ICONERROR);
            return false;
        }
    }

    return true;
}

bool DragDropHandler::IsExecutableFile(const std::string& filePath) {
    // 检查文件扩展名
    const char* ext = PathFindExtensionA(filePath.c_str());
    if (!ext) return false;

    // 支持的可执行文件扩展名
    std::vector<std::string> execExtensions = {
        ".exe", ".com", ".bat", ".cmd", ".scr", ".pif"
    };

    std::string extension = ext;
    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

    return std::find(execExtensions.begin(), execExtensions.end(), extension)
           != execExtensions.end();
}

bool DragDropHandler::IsSupportedFile(const std::string& filePath) {
    // 检查文件是否存在
    if (GetFileAttributesA(filePath.c_str()) == INVALID_FILE_ATTRIBUTES) {
        return false;
    }

    // 检查是否为可执行文件
    return IsExecutableFile(filePath);
}

void DragDropHandler::UpdateDragVisualEffect(bool entering) {
    // 更新窗口外观以指示拖拽状态
    if (entering) {
        // 拖拽进入时的视觉效果
        SetCursor(hDropCursor);

        // 可以添加边框高亮、背景色变化等效果
        // 例如：InvalidateRect(hTargetWnd, NULL, TRUE);
    } else {
        // 拖拽离开时恢复正常外观
        SetCursor(hNormalCursor);
    }
}
```

#### 主窗口中的拖拽集成
```cpp
// 在MainWindow类中集成拖拽功能
void MainWindow::SetupDragDrop() {
    // 创建拖拽处理器
    dragDropHandler = std::make_unique<DragDropHandler>(hWnd);

    // 初始化拖拽支持
    if (!dragDropHandler->Initialize()) {
        AddLogEntry("拖拽功能初始化失败", 2);
        return;
    }

    // 设置拖拽回调函数
    dragDropHandler->SetDropCallback([this](const std::vector<std::string>& files) {
        OnDragDrop(files);
    });

    dragDropHandler->SetDragEnterCallback([this]() {
        UpdateStatus("拖拽文件到此处释放...");
    });

    dragDropHandler->SetDragLeaveCallback([this]() {
        UpdateStatus("就绪");
    });

    AddLogEntry("拖拽功能已启用", 0);
}

// 处理拖拽文件事件
void MainWindow::OnDragDrop(const std::vector<std::string>& files) {
    if (files.empty()) {
        return;
    }

    // 取第一个文件作为目标程序
    std::string targetFile = files[0];

    // 更新文件路径编辑框
    SetWindowTextA(hFileEdit, targetFile.c_str());

    // 记录拖拽操作
    AddLogEntry("通过拖拽选择文件: " + targetFile, 0);

    // 如果拖拽了多个文件，显示警告
    if (files.size() > 1) {
        std::string warningMsg = "检测到多个文件，仅使用第一个文件: " + targetFile;
        AddLogEntry(warningMsg, 1); // 警告级别

        MessageBoxA(hWnd, "只能选择一个程序文件，已自动选择第一个文件。",
                   "提示", MB_OK | MB_ICONINFORMATION);
    }

    // 询问是否立即分析
    if (MessageBoxA(hWnd, "是否立即分析拖拽的程序？", "确认",
                   MB_YESNO | MB_ICONQUESTION) == IDYES) {
        OnAnalyze();
    }
}

// 在窗口过程中处理拖拽消息
LRESULT CALLBACK MainWindow::WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    MainWindow* pThis = reinterpret_cast<MainWindow*>(GetWindowLongPtr(hWnd, GWLP_USERDATA));

    switch (uMsg) {
        case WM_DROPFILES: {
            if (pThis && pThis->dragDropHandler) {
                pThis->dragDropHandler->HandleDrop(reinterpret_cast<HDROP>(wParam));
            }
            return 0;
        }

        case WM_DRAGENTER: {
            if (pThis && pThis->dragDropHandler) {
                pThis->dragDropHandler->HandleDragEnter();
            }
            return 0;
        }

        case WM_DRAGLEAVE: {
            if (pThis && pThis->dragDropHandler) {
                pThis->dragDropHandler->HandleDragLeave();
            }
            return 0;
        }

        // ... 其他消息处理
    }

    return DefWindowProc(hWnd, uMsg, wParam, lParam);
}
```

## 智能检测与自适应系统

### 扩展的RestrictionDetector类设计

#### 支持的限制类型（完整列表）
- **Mutex互斥体** - `CreateMutex`, `OpenMutex`, `CreateMutexEx`
- **文件锁定** - 独占文件访问、配置文件锁定、临时文件检测
- **注册表锁定** - 注册表键值检测、运行键检测、卸载信息检测
- **窗口类检测** - `FindWindow`, `EnumWindows`, `FindWindowEx`
- **进程名检测** - `CreateToolhelp32Snapshot`, `EnumProcesses`
- **共享内存** - `CreateFileMapping`, `OpenFileMapping`
- **命名管道** - `CreateNamedPipe`, `ConnectNamedPipe`
- **硬件ID检测** - 硬件指纹、MAC地址、CPU序列号
- **网络端口** - 端口占用检测、Socket绑定
- **WMI查询** - 系统信息查询、进程监控
- **服务检测** - Windows服务状态检查
- **环境变量** - 特定环境变量检测
- **用户会话** - 会话ID检测、桌面切换检测
- **内存映射** - 共享内存段检测
- **原子操作** - 全局原子检测

#### 机器学习检测算法

```cpp
// MLClassifier.h - 机器学习分类器
#ifndef MLCLASSIFIER_H
#define MLCLASSIFIER_H

#include <vector>
#include <string>
#include <map>
#include <memory>

// 特征向量结构
struct FeatureVector {
    std::vector<double> staticFeatures;    // 静态特征（PE结构、导入表等）
    std::vector<double> dynamicFeatures;   // 动态特征（API调用模式等）
    std::vector<double> behaviorFeatures;  // 行为特征（文件访问、注册表操作等）
    std::vector<double> stringFeatures;    // 字符串特征（特征字符串、错误消息等）
};

// 检测结果结构
struct DetectionResult {
    std::vector<RestrictionType> restrictions;  // 检测到的限制类型
    std::map<RestrictionType, double> confidence; // 每种限制的置信度
    std::vector<std::string> evidences;        // 证据列表
    double overallConfidence;                   // 总体置信度
    std::string analysisReport;                 // 分析报告
    bool requiresFallback;                      // 是否需要fallback机制
};

class MLClassifier {
private:
    // 模型权重和参数
    std::vector<std::vector<double>> weights;
    std::vector<double> biases;
    std::map<std::string, double> featureWeights;

    // 训练数据和模型
    std::vector<FeatureVector> trainingData;
    std::vector<DetectionResult> trainingLabels;

    // 模型配置
    double learningRate;
    int maxIterations;
    double convergenceThreshold;

public:
    MLClassifier();
    ~MLClassifier();

    // 模型训练
    bool TrainModel(const std::vector<FeatureVector>& features,
                   const std::vector<DetectionResult>& labels);

    // 特征提取
    FeatureVector ExtractFeatures(const std::string& exePath);

    // 预测分类
    DetectionResult Predict(const FeatureVector& features);

    // 置信度评估
    double EvaluateConfidence(const FeatureVector& features,
                             const DetectionResult& result);

    // 模型保存和加载
    bool SaveModel(const std::string& modelPath);
    bool LoadModel(const std::string& modelPath);

    // 在线学习
    void UpdateModel(const FeatureVector& features,
                    const DetectionResult& actualResult);

private:
    // 神经网络前向传播
    std::vector<double> ForwardPass(const std::vector<double>& input);

    // 反向传播
    void BackwardPass(const std::vector<double>& input,
                     const std::vector<double>& target);

    // 特征归一化
    void NormalizeFeatures(FeatureVector& features);

    // 计算损失函数
    double CalculateLoss(const std::vector<double>& predicted,
                        const std::vector<double>& actual);
};

#endif
```

#### 规则引擎设计

```cpp
// RuleEngine.h - 规则引擎
#ifndef RULEENGINE_H
#define RULEENGINE_H

#include <string>
#include <vector>
#include <map>
#include <functional>

// 规则条件类型
enum class ConditionType {
    API_IMPORT,         // API导入检测
    STRING_PATTERN,     // 字符串模式匹配
    FILE_ACCESS,        // 文件访问模式
    REGISTRY_ACCESS,    // 注册表访问模式
    MEMORY_PATTERN,     // 内存模式匹配
    BEHAVIOR_PATTERN,   // 行为模式匹配
    VERSION_INFO,       // 版本信息检测
    DIGITAL_SIGNATURE   // 数字签名检测
};

// 规则条件结构
struct RuleCondition {
    ConditionType type;
    std::string pattern;        // 匹配模式
    std::string target;         // 目标对象
    double weight;              // 权重
    bool isRequired;            // 是否必需
};

// 检测规则结构
struct DetectionRule {
    std::string ruleName;                   // 规则名称
    RestrictionType restrictionType;        // 限制类型
    std::vector<RuleCondition> conditions;  // 条件列表
    double threshold;                       // 阈值
    int priority;                          // 优先级
    std::string description;               // 描述
    std::vector<std::string> bypassMethods; // 推荐绕过方法
};

class RuleEngine {
private:
    std::vector<DetectionRule> rules;       // 规则库
    std::map<std::string, std::function<bool(const std::string&, const std::string&)>>
        conditionEvaluators;                // 条件评估器

public:
    RuleEngine();
    ~RuleEngine();

    // 规则管理
    bool LoadRules(const std::string& rulesFile);
    bool SaveRules(const std::string& rulesFile);
    void AddRule(const DetectionRule& rule);
    void RemoveRule(const std::string& ruleName);

    // 规则评估
    DetectionResult EvaluateRules(const std::string& exePath);

    // 条件评估
    bool EvaluateCondition(const RuleCondition& condition,
                          const std::string& exePath);

    // 优先级排序
    std::vector<DetectionRule> SortRulesByPriority();

    // 规则统计
    void UpdateRuleStatistics(const std::string& ruleName, bool success);

private:
    // 注册条件评估器
    void RegisterConditionEvaluators();

    // API导入检测
    bool EvaluateAPIImport(const std::string& exePath, const std::string& apiName);

    // 字符串模式匹配
    bool EvaluateStringPattern(const std::string& exePath, const std::string& pattern);

    // 文件访问模式检测
    bool EvaluateFileAccess(const std::string& exePath, const std::string& pattern);

    // 注册表访问模式检测
    bool EvaluateRegistryAccess(const std::string& exePath, const std::string& pattern);
};

#endif
```

#### 置信度评估和Fallback机制

```cpp
// ConfidenceEvaluator.h - 置信度评估器
#ifndef CONFIDENCEEVALUATOR_H
#define CONFIDENCEEVALUATOR_H

#include <vector>
#include <map>
#include <string>

// 置信度因子
struct ConfidenceFactor {
    std::string factorName;     // 因子名称
    double weight;              // 权重
    double value;               // 值
    std::string evidence;       // 证据
};

// 置信度评估结果
struct ConfidenceAssessment {
    double overallConfidence;                           // 总体置信度
    std::map<RestrictionType, double> typeConfidence;  // 各类型置信度
    std::vector<ConfidenceFactor> factors;             // 置信度因子
    std::vector<std::string> uncertainties;           // 不确定性因素
    bool recommendFallback;                            // 是否推荐fallback
    std::string assessmentReport;                      // 评估报告
};

class ConfidenceEvaluator {
private:
    // 评估参数
    double minConfidenceThreshold;      // 最小置信度阈值
    double fallbackThreshold;           // Fallback阈值
    std::map<std::string, double> factorWeights; // 因子权重

    // 历史数据
    std::map<std::string, std::vector<double>> historicalAccuracy; // 历史准确率

public:
    ConfidenceEvaluator();
    ~ConfidenceEvaluator();

    // 置信度评估
    ConfidenceAssessment EvaluateConfidence(const DetectionResult& mlResult,
                                           const DetectionResult& ruleResult,
                                           const std::string& exePath);

    // 因子计算
    std::vector<ConfidenceFactor> CalculateConfidenceFactors(
        const DetectionResult& result, const std::string& exePath);

    // Fallback决策
    bool ShouldUseFallback(const ConfidenceAssessment& assessment);

    // 历史数据更新
    void UpdateHistoricalData(const std::string& appName,
                             const DetectionResult& predicted,
                             const DetectionResult& actual);

    // 阈值调整
    void AdjustThresholds(double successRate);

private:
    // 计算静态分析置信度
    double CalculateStaticConfidence(const DetectionResult& result);

    // 计算动态分析置信度
    double CalculateDynamicConfidence(const DetectionResult& result);

    // 计算一致性置信度
    double CalculateConsistencyConfidence(const DetectionResult& mlResult,
                                        const DetectionResult& ruleResult);

    // 计算历史准确率
    double CalculateHistoricalAccuracy(const std::string& appName);
};

#endif
```

#### 检测流程优化

```cpp
// 完整的检测流程实现
DetectionResult RestrictionDetector::AnalyzeApplication(const std::string& exePath) {
    DetectionResult finalResult;

    try {
        // 第一阶段：快速预检测
        AddLogEntry("开始快速预检测...", 0);
        DetectionResult quickScan = PerformQuickScan(exePath);

        if (quickScan.overallConfidence > 0.9) {
            AddLogEntry("快速预检测置信度较高，跳过详细分析", 0);
            return quickScan;
        }

        // 第二阶段：静态分析
        AddLogEntry("执行静态分析...", 0);
        DetectionResult staticResult = staticAnalyzer->AnalyzePE(exePath);

        // 第三阶段：动态分析（如果需要）
        DetectionResult dynamicResult;
        if (staticResult.overallConfidence < 0.7) {
            AddLogEntry("执行动态分析...", 0);
            dynamicResult = dynamicAnalyzer->AnalyzeRuntime(exePath);
        }

        // 第四阶段：机器学习分类
        AddLogEntry("执行机器学习分类...", 0);
        FeatureVector features = mlClassifier->ExtractFeatures(exePath);
        DetectionResult mlResult = mlClassifier->Predict(features);

        // 第五阶段：规则引擎评估
        AddLogEntry("执行规则引擎评估...", 0);
        DetectionResult ruleResult = ruleEngine->EvaluateRules(exePath);

        // 第六阶段：结果融合和置信度评估
        AddLogEntry("融合检测结果...", 0);
        finalResult = FuseResults(staticResult, dynamicResult, mlResult, ruleResult);

        // 第七阶段：置信度评估
        ConfidenceAssessment confidence = confidenceEvaluator->EvaluateConfidence(
            mlResult, ruleResult, exePath);

        finalResult.overallConfidence = confidence.overallConfidence;
        finalResult.requiresFallback = confidence.recommendFallback;

        // 第八阶段：Fallback机制
        if (finalResult.requiresFallback) {
            AddLogEntry("触发Fallback机制...", 1);
            DetectionResult fallbackResult = PerformFallbackDetection(exePath);
            finalResult = MergeWithFallback(finalResult, fallbackResult);
        }

        // 生成分析报告
        finalResult.analysisReport = GenerateAnalysisReport(finalResult, confidence);

        AddLogEntry("检测完成，总体置信度: " +
                   std::to_string(finalResult.overallConfidence * 100) + "%", 0);

    } catch (const std::exception& e) {
        AddLogEntry("检测过程发生异常: " + std::string(e.what()), 2);
        finalResult.overallConfidence = 0.0;
        finalResult.requiresFallback = true;
    }

    return finalResult;
}
```

### 2. 绕过策略

#### Mutex绕过
```cpp
// 策略1: API Hook - 修改Mutex名称
HANDLE HookedCreateMutex(LPCSTR lpName) {
    string newName = string(lpName) + "_" + to_string(GetCurrentProcessId());
    return TrueCreateMutex(newName.c_str());
}

// 策略2: 内存补丁 - 直接修改内存中的Mutex名
bool PatchMutexInMemory(HANDLE hProcess, DWORD address);

// 策略3: 进程隔离 - 独立的对象命名空间
bool CreateIsolatedNamespace(DWORD processId);
```

#### 文件锁定绕过
```cpp
// 策略1: 文件重定向
bool SetupFileRedirection(const string& originalPath, const string& redirectPath);

// 策略2: 虚拟文件系统
bool CreateVirtualFileSystem(DWORD processId);

// 策略3: 文件句柄复制
bool DuplicateFileHandles(HANDLE hSourceProcess, HANDLE hTargetProcess);
```

#### 注册表绕过
```cpp
// 策略1: 注册表虚拟化
bool CreateVirtualRegistry(const string& basePath);

// 策略2: API重定向
LONG HookedRegOpenKey(HKEY hKey, LPCSTR lpSubKey, PHKEY phkResult);

// 策略3: 注册表快照
bool CreateRegistrySnapshot(const string& keyPath);
```

## 高级反检测与隐蔽技术

### DLL隐藏和内存清理技术

#### DLL隐藏实现
```cpp
// DLLHiding.h - DLL隐藏技术
#ifndef DLLHIDING_H
#define DLLHIDING_H

#include <windows.h>
#include <winternl.h>
#include <string>
#include <vector>

// PEB_LDR_DATA结构（未文档化）
typedef struct _PEB_LDR_DATA {
    ULONG Length;
    BOOLEAN Initialized;
    PVOID SsHandle;
    LIST_ENTRY InLoadOrderModuleList;
    LIST_ENTRY InMemoryOrderModuleList;
    LIST_ENTRY InInitializationOrderModuleList;
} PEB_LDR_DATA, *PPEB_LDR_DATA;

// LDR_DATA_TABLE_ENTRY结构（未文档化）
typedef struct _LDR_DATA_TABLE_ENTRY {
    LIST_ENTRY InLoadOrderLinks;
    LIST_ENTRY InMemoryOrderLinks;
    LIST_ENTRY InInitializationOrderLinks;
    PVOID DllBase;
    PVOID EntryPoint;
    ULONG SizeOfImage;
    UNICODE_STRING FullDllName;
    UNICODE_STRING BaseDllName;
    ULONG Flags;
    USHORT LoadCount;
    USHORT TlsIndex;
    LIST_ENTRY HashLinks;
    PVOID SectionPointer;
    ULONG CheckSum;
    ULONG TimeDateStamp;
    PVOID LoadedImports;
    PVOID EntryPointActivationContext;
    PVOID PatchInformation;
} LDR_DATA_TABLE_ENTRY, *PLDR_DATA_TABLE_ENTRY;

class DLLHiding {
private:
    std::vector<HMODULE> hiddenModules;     // 已隐藏的模块列表
    std::vector<std::string> hiddenPaths;   // 已隐藏的路径列表

public:
    DLLHiding();
    ~DLLHiding();

    // 从PEB中隐藏DLL
    bool HideDLLFromPEB(HMODULE hModule);
    bool HideDLLFromPEB(const std::string& dllName);

    // 从模块列表中移除DLL
    bool RemoveFromModuleList(HMODULE hModule);

    // 隐藏DLL文件路径
    bool HideDLLPath(const std::string& dllPath);

    // 恢复隐藏的DLL
    bool RestoreHiddenDLL(HMODULE hModule);

    // 检查DLL是否已隐藏
    bool IsDLLHidden(HMODULE hModule);

    // 获取PEB地址
    PPEB GetCurrentPEB();

    // 遍历模块列表
    std::vector<HMODULE> EnumerateLoadedModules();

private:
    // 从链表中移除节点
    void UnlinkListEntry(PLIST_ENTRY entry);

    // 重新链接节点
    void RelinkListEntry(PLIST_ENTRY entry, PLIST_ENTRY before, PLIST_ENTRY after);

    // 查找模块在PEB中的条目
    PLDR_DATA_TABLE_ENTRY FindModuleEntry(HMODULE hModule);

    // 备份模块信息
    void BackupModuleInfo(HMODULE hModule);
};

#endif
```

#### 内存清理技术实现
```cpp
// MemoryCleaner.h - 内存清理技术
#ifndef MEMORYCLEANER_H
#define MEMORYCLEANER_H

#include <windows.h>
#include <vector>
#include <map>
#include <string>

// 内存区域信息
struct MemoryRegion {
    PVOID baseAddress;      // 基地址
    SIZE_T size;            // 大小
    DWORD protection;       // 保护属性
    DWORD type;             // 类型
    std::string description; // 描述
};

// 清理策略
enum class CleanupStrategy {
    ZERO_FILL,              // 零填充
    RANDOM_FILL,            // 随机填充
    PATTERN_FILL,           // 模式填充
    SECURE_ERASE            // 安全擦除
};

class MemoryCleaner {
private:
    std::vector<MemoryRegion> trackedRegions;   // 跟踪的内存区域
    std::map<PVOID, std::vector<BYTE>> backups; // 内存备份
    bool isCleanupEnabled;                      // 清理是否启用

public:
    MemoryCleaner();
    ~MemoryCleaner();

    // 跟踪内存区域
    void TrackMemoryRegion(PVOID address, SIZE_T size, const std::string& description);

    // 清理指定内存区域
    bool CleanMemoryRegion(PVOID address, SIZE_T size, CleanupStrategy strategy);

    // 清理所有跟踪的区域
    bool CleanAllTrackedRegions(CleanupStrategy strategy);

    // 清理DLL注入痕迹
    bool CleanInjectionTraces(HMODULE hModule);

    // 清理Hook痕迹
    bool CleanHookTraces(PVOID originalFunction, PVOID hookFunction);

    // 清理调试信息
    bool CleanDebugInfo();

    // 清理环境变量
    bool CleanEnvironmentVariables();

    // 备份内存内容
    bool BackupMemory(PVOID address, SIZE_T size);

    // 恢复内存内容
    bool RestoreMemory(PVOID address);

    // 安全擦除内存
    bool SecureErase(PVOID address, SIZE_T size, int passes = 3);

private:
    // 生成随机数据
    void GenerateRandomData(PBYTE buffer, SIZE_T size);

    // 生成模式数据
    void GeneratePatternData(PBYTE buffer, SIZE_T size, BYTE pattern);

    // 检查内存区域是否可写
    bool IsMemoryWritable(PVOID address, SIZE_T size);

    // 修改内存保护属性
    bool ChangeMemoryProtection(PVOID address, SIZE_T size, DWORD newProtection, DWORD& oldProtection);
};

#endif
```

### Hook链隐藏和API调用伪装

#### Hook链隐藏实现
```cpp
// HookChainHiding.h - Hook链隐藏技术
#ifndef HOOKCHAINHIDING_H
#define HOOKCHAINHIDING_H

#include <windows.h>
#include <vector>
#include <map>
#include <string>

// Hook信息结构
struct HookInfo {
    PVOID originalFunction;     // 原始函数地址
    PVOID hookFunction;         // Hook函数地址
    PVOID trampolineFunction;   // 跳板函数地址
    std::vector<BYTE> originalBytes; // 原始字节
    std::string functionName;   // 函数名称
    bool isHidden;              // 是否已隐藏
};

// Hook类型
enum class HookType {
    INLINE_HOOK,        // 内联Hook
    IAT_HOOK,           // 导入表Hook
    EAT_HOOK,           // 导出表Hook
    VTABLE_HOOK,        // 虚表Hook
    SSDT_HOOK           // 系统服务表Hook
};

class HookChainHiding {
private:
    std::map<PVOID, HookInfo> installedHooks;   // 已安装的Hook
    std::vector<PVOID> hiddenHooks;             // 已隐藏的Hook
    bool isHidingEnabled;                       // 隐藏是否启用

public:
    HookChainHiding();
    ~HookChainHiding();

    // 注册Hook
    bool RegisterHook(PVOID originalFunction, PVOID hookFunction,
                     PVOID trampolineFunction, const std::string& functionName);

    // 隐藏Hook链
    bool HideHookChain(PVOID originalFunction);

    // 隐藏所有Hook
    bool HideAllHooks();

    // 恢复Hook链
    bool RestoreHookChain(PVOID originalFunction);

    // 伪装API调用
    bool DisguiseAPICall(PVOID targetFunction, PVOID disguiseFunction);

    // 创建假的调用栈
    bool CreateFakeCallStack();

    // 清理Hook痕迹
    bool CleanHookTraces(PVOID originalFunction);

    // 检测Hook链扫描
    bool DetectHookScanning();

    // 反Hook检测
    bool AntiHookDetection();

private:
    // 修改函数入口点
    bool ModifyFunctionEntry(PVOID function, const std::vector<BYTE>& newBytes);

    // 创建跳板函数
    PVOID CreateTrampoline(PVOID originalFunction, SIZE_T hookSize);

    // 计算Hook大小
    SIZE_T CalculateHookSize(PVOID function);

    // 检查函数是否被Hook
    bool IsFunctionHooked(PVOID function);

    // 获取函数的原始字节
    std::vector<BYTE> GetOriginalBytes(PVOID function, SIZE_T size);
};

#endif
```

### 反调试检测的绕过策略

#### 反反调试实现
```cpp
// AntiAntiDebug.h - 反反调试技术
#ifndef ANTIANTIDEBUG_H
#define ANTIANTIDEBUG_H

#include <windows.h>
#include <winternl.h>
#include <vector>
#include <map>
#include <functional>

// 反调试检测类型
enum class AntiDebugType {
    ISDEBUGGER_PRESENT,     // IsDebuggerPresent检测
    PEB_CHECK,              // PEB检查
    HEAP_FLAGS,             // 堆标志检查
    NTGlobalFlag,           // NTGlobalFlag检查
    QUERY_INFORMATION,      // 查询信息检测
    DEBUG_PORT,             // 调试端口检测
    TIMING_CHECK,           // 时间检测
    EXCEPTION_CHECK,        // 异常检测
    HARDWARE_BREAKPOINT,    // 硬件断点检测
    SOFTWARE_BREAKPOINT,    // 软件断点检测
    MEMORY_PROTECTION,      // 内存保护检测
    THREAD_CONTEXT,         // 线程上下文检测
    PROCESS_ENVIRONMENT     // 进程环境检测
};

// 绕过策略
struct BypassStrategy {
    AntiDebugType type;
    std::function<bool()> bypassFunction;
    std::string description;
    bool isActive;
};

class AntiAntiDebug {
private:
    std::map<AntiDebugType, BypassStrategy> strategies; // 绕过策略
    std::vector<PVOID> hookedFunctions;                // 已Hook的函数
    bool isProtectionActive;                           // 保护是否激活

public:
    AntiAntiDebug();
    ~AntiAntiDebug();

    // 初始化反反调试保护
    bool InitializeProtection();

    // 绕过IsDebuggerPresent检测
    bool BypassIsDebuggerPresent();

    // 绕过PEB检查
    bool BypassPEBCheck();

    // 绕过堆标志检查
    bool BypassHeapFlags();

    // 绕过NTGlobalFlag检查
    bool BypassNTGlobalFlag();

    // 绕过查询信息检测
    bool BypassQueryInformation();

    // 绕过调试端口检测
    bool BypassDebugPort();

    // 绕过时间检测
    bool BypassTimingCheck();

    // 绕过异常检测
    bool BypassExceptionCheck();

    // 绕过硬件断点检测
    bool BypassHardwareBreakpoint();

    // 绕过软件断点检测
    bool BypassSoftwareBreakpoint();

    // 启用所有绕过策略
    bool EnableAllBypasses();

    // 禁用所有绕过策略
    bool DisableAllBypasses();

    // 检测反调试技术
    std::vector<AntiDebugType> DetectAntiDebugTechniques(const std::string& exePath);

private:
    // Hook API函数
    bool HookAPIFunction(const std::string& moduleName, const std::string& functionName,
                        PVOID hookFunction, PVOID& originalFunction);

    // 修改PEB结构
    bool ModifyPEB();

    // 修改堆结构
    bool ModifyHeapFlags();

    // 伪造时间信息
    bool FakeTimingInfo();

    // 处理异常
    LONG WINAPI ExceptionHandler(PEXCEPTION_POINTERS pExceptionInfo);

    // 清理调试痕迹
    bool CleanDebugTraces();
};

// Hook函数声明
BOOL WINAPI HookedIsDebuggerPresent();
NTSTATUS WINAPI HookedNtQueryInformationProcess(HANDLE ProcessHandle,
    PROCESSINFOCLASS ProcessInformationClass, PVOID ProcessInformation,
    ULONG ProcessInformationLength, PULONG ReturnLength);
DWORD WINAPI HookedGetTickCount();
BOOL WINAPI HookedQueryPerformanceCounter(LARGE_INTEGER* lpPerformanceCount);

#endif
```

### 虚拟环境检测应对方案

#### VMware和VirtualBox检测绕过
```cpp
// VMEvasion.h - 虚拟机逃逸技术
#ifndef VMEVASION_H
#define VMEVASION_H

#include <windows.h>
#include <string>
#include <vector>
#include <map>

// 虚拟机类型
enum class VMType {
    VMWARE,
    VIRTUALBOX,
    HYPER_V,
    QEMU,
    XEN,
    PARALLELS,
    UNKNOWN
};

// 检测方法
enum class DetectionMethod {
    REGISTRY_CHECK,         // 注册表检查
    FILE_CHECK,             // 文件检查
    PROCESS_CHECK,          // 进程检查
    SERVICE_CHECK,          // 服务检查
    HARDWARE_CHECK,         // 硬件检查
    TIMING_CHECK,           // 时间检查
    CPUID_CHECK,            // CPUID检查
    MAC_ADDRESS_CHECK,      // MAC地址检查
    MEMORY_CHECK,           // 内存检查
    DEVICE_CHECK            // 设备检查
};

// 虚拟机特征
struct VMSignature {
    VMType type;
    DetectionMethod method;
    std::string signature;
    std::string description;
};

class VMEvasion {
private:
    std::vector<VMSignature> vmSignatures;      // 虚拟机特征库
    std::map<VMType, bool> detectedVMs;         // 检测到的虚拟机
    bool isEvasionActive;                       // 逃逸是否激活

public:
    VMEvasion();
    ~VMEvasion();

    // 初始化虚拟机逃逸
    bool InitializeEvasion();

    // 检测虚拟机环境
    std::vector<VMType> DetectVirtualMachines();

    // 伪装物理机环境
    bool DisguiseAsPhysicalMachine();

    // 绕过VMware检测
    bool BypassVMwareDetection();

    // 绕过VirtualBox检测
    bool BypassVirtualBoxDetection();

    // 绕过Hyper-V检测
    bool BypassHyperVDetection();

    // 修改注册表特征
    bool ModifyRegistrySignatures();

    // 隐藏虚拟机文件
    bool HideVMFiles();

    // 伪造硬件信息
    bool FakeHardwareInfo();

    // 修改MAC地址
    bool ModifyMACAddress();

    // 伪造CPUID信息
    bool FakeCPUIDInfo();

    // 调整时间特征
    bool AdjustTimingCharacteristics();

private:
    // 加载虚拟机特征库
    void LoadVMSignatures();

    // 检查注册表特征
    bool CheckRegistrySignature(const VMSignature& signature);

    // 检查文件特征
    bool CheckFileSignature(const VMSignature& signature);

    // 检查进程特征
    bool CheckProcessSignature(const VMSignature& signature);

    // 检查服务特征
    bool CheckServiceSignature(const VMSignature& signature);

    // 检查硬件特征
    bool CheckHardwareSignature(const VMSignature& signature);

    // Hook相关API
    bool HookVMDetectionAPIs();

    // 创建假的硬件设备
    bool CreateFakeHardwareDevices();
};

#endif
```

### 进程注入痕迹清除方法

#### 进程注入技术增强
```cpp
class ProcessInjector {
public:
    // 方法1: CreateRemoteThread + LoadLibrary（增强版）
    bool InjectDLL_CreateRemoteThread(DWORD processId, const string& dllPath);

    // 方法2: Manual DLL Mapping（隐蔽版）
    bool InjectDLL_ManualMapping(DWORD processId, const string& dllPath);

    // 方法3: SetWindowsHookEx（全局Hook）
    bool InjectDLL_SetHook(DWORD processId, const string& dllPath);

    // 方法4: NtCreateThreadEx（原生API）
    bool InjectDLL_NtCreateThread(DWORD processId, const string& dllPath);

    // 方法5: Process Hollowing（进程挖空）
    bool InjectDLL_ProcessHollowing(const string& targetProcess, const string& dllPath);

    // 方法6: Atom Bombing（原子轰炸）
    bool InjectDLL_AtomBombing(DWORD processId, const string& dllPath);

    // 方法7: Thread Execution Hijacking（线程劫持）
    bool InjectDLL_ThreadHijacking(DWORD processId, const string& dllPath);

    // 清除注入痕迹
    bool CleanInjectionTraces(DWORD processId, HMODULE hModule);

private:
    // 清除远程线程痕迹
    bool CleanRemoteThreadTraces(HANDLE hThread);

    // 清除内存分配痕迹
    bool CleanMemoryAllocationTraces(HANDLE hProcess, PVOID address);

    // 清除模块加载痕迹
    bool CleanModuleLoadTraces(HANDLE hProcess, HMODULE hModule);
};
```

## API参考

### 主要类接口

#### UniversalMultiLauncher
```cpp
class UniversalMultiLauncher {
public:
    bool Initialize();
    DetectionResult AnalyzeApplication(const string& exePath);
    bool LaunchWithBypass(const string& exePath, int instanceCount = 1);
    bool SaveAnalysisResult(const string& exePath, const DetectionResult& result);
    bool LoadKnownApps();
    
private:
    map<string, DetectionResult> appDatabase;
    vector<BypassStrategy> strategies;
};
```

#### RestrictionDetector
```cpp
class RestrictionDetector {
public:
    DetectionResult AnalyzeApp(const string& exePath);
    bool AnalyzePEImports(const string& exePath, DetectionResult& result);
    bool MonitorRuntimeBehavior(const string& exePath, DetectionResult& result);
    bool ScanMemoryPatterns(DetectionResult& result);
    
private:
    bool FindAPIInImports(const string& exePath, const string& apiName);
    void ClassifyRestrictionType(const string& apiName, DetectionResult& result);
};
```

#### BypassEngine
```cpp
class BypassEngine {
public:
    bool ApplyBypass(const string& exePath, const DetectionResult& detection);
    bool BypassMutex(const string& exePath, const DetectionResult& detection);
    bool BypassFileLock(const string& exePath, const DetectionResult& detection);
    bool BypassRegistryLock(const string& exePath, const DetectionResult& detection);
    
private:
    bool InjectHookDLL(const string& exePath, const string& dllPath, const string& hookName);
    string CreateInstanceDirectory(const string& exePath);
    string CreateVirtualRegistryBranch();
};
```

## 编译指南

### Visual Studio编译
```batch
# 1. 打开Developer Command Prompt
# 2. 编译主程序
cl /EHsc /I"include" /I"libs/detours/include" src/main.cpp src/core/*.cpp src/utils/*.cpp /link libs/detours/lib.X64/detours.lib psapi.lib advapi32.lib kernel32.lib user32.lib /OUT:MultiLauncher.exe

# 3. 编译Hook DLL
cl /LD /EHsc /I"libs/detours/include" src/hooks/mutex_hook.cpp /link libs/detours/lib.X64/detours.lib /OUT:mutex_hook.dll
cl /LD /EHsc /I"libs/detours/include" src/hooks/file_hook.cpp /link libs/detours/lib.X64/detours.lib /OUT:file_hook.dll
cl /LD /EHsc /I"libs/detours/include" src/hooks/registry_hook.cpp /link libs/detours/lib.X64/detours.lib /OUT:registry_hook.dll
cl /LD /EHsc /I"libs/detours/include" src/hooks/window_hook.cpp /link libs/detours/lib.X64/detours.lib /OUT:window_hook.dll
```

### CMake编译
```batch
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

### MinGW编译
```batch
g++ -std=c++17 -O2 -I"include" -I"libs/detours/include" src/main.cpp src/core/*.cpp src/utils/*.cpp -L"libs/detours/lib" -ldetours -lpsapi -ladvapi32 -lkernel32 -luser32 -lshlwapi -o MultiLauncher.exe

g++ -shared -std=c++17 -O2 -I"libs/detours/include" src/hooks/mutex_hook.cpp -L"libs/detours/lib" -ldetours -o mutex_hook.dll
```

## 测试指南

### 单元测试
```cpp
// tests/test_detector.cpp
#include "gtest/gtest.h"
#include "RestrictionDetector.h"

TEST(RestrictionDetectorTest, DetectMutex) {
    RestrictionDetector detector;
    DetectionResult result = detector.AnalyzeApp("test_apps/mutex_app.exe");
    
    EXPECT_TRUE(find(result.restrictions.begin(), result.restrictions.end(), 
                    RestrictionType::MUTEX_LOCK) != result.restrictions.end());
}

TEST(RestrictionDetectorTest, DetectFileLock) {
    RestrictionDetector detector;
    DetectionResult result = detector.AnalyzeApp("test_apps/file_lock_app.exe");
    
    EXPECT_TRUE(find(result.restrictions.begin(), result.restrictions.end(), 
                    RestrictionType::FILE_LOCK) != result.restrictions.end());
}
```

### 集成测试
```cpp
// tests/test_integration.cpp
TEST(IntegrationTest, LaunchMultipleInstances) {
    UniversalMultiLauncher launcher;
    ASSERT_TRUE(launcher.Initialize());
    
    string testApp = "test_apps/sample_app.exe";
    EXPECT_TRUE(launcher.LaunchWithBypass(testApp, 3));
    
    // 验证确实启动了3个实例
    int instanceCount = CountProcessInstances("sample_app.exe");
    EXPECT_EQ(instanceCount, 3);
}
```

### 测试应用程序
创建简单的测试程序来验证各种限制机制：

```cpp
// test_apps/mutex_test.cpp - Mutex限制测试
int main() {
    HANDLE hMutex = CreateMutexA(NULL, TRUE, "TestAppMutex");
    if (GetLastError() == ERROR_ALREADY_EXISTS) {
        MessageBoxA(NULL, "应用程序已在运行", "错误", MB_OK);
        return 1;
    }
    
    MessageBoxA(NULL, "应用程序启动成功", "信息", MB_OK);
    CloseHandle(hMutex);
    return 0;
}
```

## 用户交互流程

### 完整操作步骤

#### 1. 程序启动和初始化
```
用户启动程序 → 加载配置文件 → 初始化检测引擎 → 显示主界面
```

**详细流程：**
1. **程序启动**：双击MultiLauncher.exe或通过命令行启动
2. **权限检查**：自动检测并请求管理员权限（如需要）
3. **环境检测**：检测操作系统版本、.NET Framework版本等
4. **配置加载**：加载用户设置、规则库、特征数据库
5. **界面初始化**：创建主窗口、设置拖拽支持、加载主题

#### 2. 软件选择阶段
```
方式A: 文件选择对话框
用户点击"浏览"按钮 → 打开文件选择对话框 → 选择目标程序 → 自动填充路径

方式B: 拖拽操作
用户拖拽文件到程序窗口 → 验证文件类型 → 自动填充路径 → 显示确认提示
```

**界面反馈：**
- 文件路径实时显示在编辑框中
- 状态栏显示"已选择文件：xxx.exe"
- 自动启用"分析"和"启动"按钮
- 显示文件基本信息（大小、版本、数字签名状态）

#### 3. 程序分析阶段
```
用户点击"分析"按钮 → 显示进度对话框 → 执行多阶段检测 → 显示检测结果
```

**分析进度显示：**
```
┌─分析进度─────────────────────────────────────┐
│ 当前阶段: 静态分析 - PE文件结构解析          │
│ 进度: ████████████████████████████ 75%      │
│                                             │
│ 已完成:                                     │
│ ✓ 文件完整性检查                            │
│ ✓ 数字签名验证                              │
│ ✓ PE导入表分析                              │
│ ✓ 字符串特征提取                            │
│                                             │
│ 正在执行:                                   │
│ ⟳ 动态行为分析                              │
│                                             │
│ 待执行:                                     │
│ ○ 机器学习分类                              │
│ ○ 规则引擎评估                              │
│ ○ 置信度计算                                │
│                                             │
│ [取消分析]                                  │
└─────────────────────────────────────────────┘
```

#### 4. 结果展示阶段
```
分析完成 → 更新检测结果列表 → 显示置信度 → 提供绕过建议
```

**检测结果界面：**
```
┌─检测结果─────────────────────────────────────┐
│ 检测项目          状态    置信度    证据数量  │
│ ─────────────────────────────────────────── │
│ Mutex互斥体       ✓检测到   95%      3      │
│ 文件锁定          ✓检测到   87%      2      │
│ 注册表检查        ✗未检测   12%      0      │
│ 窗口类检测        ✓检测到   78%      1      │
│ 进程名检测        ✗未检测   23%      0      │
│ 共享内存          ✓检测到   91%      2      │
│ ─────────────────────────────────────────── │
│ 总体评估: 高限制程度 (置信度: 84%)           │
│ 推荐策略: Mutex Hook + 文件重定向            │
│                                             │
│ [查看详细报告] [导出结果] [开始绕过]         │
└─────────────────────────────────────────────┘
```

#### 5. 多开启动阶段
```
用户设置实例数量 → 点击"启动多开" → 执行绕过策略 → 启动多个实例
```

**启动进度显示：**
```
┌─多开启动─────────────────────────────────────┐
│ 目标程序: TestApp.exe                       │
│ 实例数量: 3                                 │
│ 当前进度: 正在启动第2个实例...               │
│                                             │
│ 实例状态:                                   │
│ 实例1: ✓已启动 (PID: 1234) - 运行正常       │
│ 实例2: ⟳启动中... 应用绕过策略              │
│ 实例3: ○等待启动                            │
│                                             │
│ 绕过策略:                                   │
│ ✓ Mutex Hook已注入                          │
│ ✓ 文件重定向已设置                          │
│ ✓ 注册表虚拟化已启用                        │
│                                             │
│ [停止启动] [查看日志]                       │
└─────────────────────────────────────────────┘
```

### 错误处理和用户提示

#### 常见错误场景处理
```cpp
// 错误处理示例
class ErrorHandler {
public:
    // 文件选择错误
    static void HandleFileSelectionError(const std::string& error) {
        std::string message = "文件选择失败：\n" + error +
                             "\n\n请确保：\n"
                             "1. 文件确实存在\n"
                             "2. 文件是有效的可执行文件\n"
                             "3. 您有足够的访问权限";
        MessageBoxA(NULL, message.c_str(), "文件选择错误", MB_OK | MB_ICONERROR);
    }

    // 权限不足错误
    static void HandlePermissionError() {
        std::string message = "权限不足，无法执行操作。\n\n"
                             "请尝试：\n"
                             "1. 以管理员身份运行程序\n"
                             "2. 检查目标程序是否被其他安全软件保护\n"
                             "3. 暂时关闭杀毒软件的实时保护";
        MessageBoxA(NULL, message.c_str(), "权限错误", MB_OK | MB_ICONWARNING);
    }

    // 分析失败错误
    static void HandleAnalysisError(const std::string& details) {
        std::string message = "程序分析失败：\n" + details +
                             "\n\n可能的原因：\n"
                             "1. 目标程序使用了未知的保护技术\n"
                             "2. 程序文件已损坏或被加密\n"
                             "3. 系统资源不足\n\n"
                             "建议：尝试使用手动模式或联系技术支持";
        MessageBoxA(NULL, message.c_str(), "分析错误", MB_OK | MB_ICONERROR);
    }

    // 启动失败错误
    static void HandleLaunchError(int instanceNumber, const std::string& error) {
        char message[512];
        sprintf_s(message, "第%d个实例启动失败：\n%s\n\n"
                          "可能的解决方案：\n"
                          "1. 检查目标程序是否需要特定的运行环境\n"
                          "2. 确认系统资源充足\n"
                          "3. 尝试降低同时启动的实例数量",
                  instanceNumber, error.c_str());
        MessageBoxA(NULL, message, "启动错误", MB_OK | MB_ICONERROR);
    }
};
```

### 高级功能操作指南

#### 批量处理模式
```
1. 点击菜单"工具" → "批量处理"
2. 添加多个目标程序到列表
3. 为每个程序设置实例数量
4. 点击"开始批量处理"
5. 监控整体进度和各程序状态
```

#### 配置文件管理
```
1. 点击菜单"文件" → "配置管理"
2. 选择"导入配置"或"导出配置"
3. 选择配置文件路径
4. 确认导入/导出操作
```

#### 日志查看和导出
```
1. 在主界面点击"查看日志"按钮
2. 选择日志级别过滤（信息/警告/错误）
3. 使用搜索功能查找特定内容
4. 点击"导出日志"保存到文件
```

## 性能基准测试

### 性能指标定义

#### 内存使用指标
- **基础内存占用**：程序启动后的初始内存使用量
- **峰值内存占用**：分析过程中的最大内存使用量
- **内存增长率**：随实例数量增加的内存增长情况
- **内存泄漏检测**：长时间运行后的内存变化

#### CPU使用指标
- **空闲状态CPU占用**：程序待机时的CPU使用率
- **分析阶段CPU占用**：执行检测分析时的CPU使用率
- **启动阶段CPU占用**：多开启动时的CPU使用率
- **CPU使用峰值**：各阶段的最大CPU使用率

#### 响应时间指标
- **界面响应时间**：用户操作到界面反馈的时间
- **文件分析时间**：完成程序分析所需的时间
- **实例启动时间**：单个实例启动所需的时间
- **总体处理时间**：从开始到完成的总时间

### 基准测试实现

```cpp
// PerformanceBenchmark.h - 性能基准测试
#ifndef PERFORMANCEBENCHMARK_H
#define PERFORMANCEBENCHMARK_H

#include <windows.h>
#include <psapi.h>
#include <string>
#include <vector>
#include <chrono>
#include <map>

// 性能指标结构
struct PerformanceMetrics {
    // 内存指标
    SIZE_T baseMemoryUsage;         // 基础内存使用(KB)
    SIZE_T peakMemoryUsage;         // 峰值内存使用(KB)
    SIZE_T currentMemoryUsage;      // 当前内存使用(KB)
    double memoryGrowthRate;        // 内存增长率(%)

    // CPU指标
    double idleCPUUsage;            // 空闲CPU使用率(%)
    double analysisCPUUsage;        // 分析CPU使用率(%)
    double launchCPUUsage;          // 启动CPU使用率(%)
    double peakCPUUsage;            // 峰值CPU使用率(%)

    // 时间指标
    DWORD uiResponseTime;           // 界面响应时间(ms)
    DWORD analysisTime;             // 分析时间(ms)
    DWORD launchTime;               // 启动时间(ms)
    DWORD totalProcessTime;         // 总处理时间(ms)

    // 其他指标
    int successfulLaunches;         // 成功启动数量
    int failedLaunches;             // 失败启动数量
    double successRate;             // 成功率(%)
};

// 测试场景
enum class TestScenario {
    LIGHT_LOAD,         // 轻负载（1-2个实例）
    MEDIUM_LOAD,        // 中负载（3-5个实例）
    HEAVY_LOAD,         // 重负载（6-10个实例）
    STRESS_TEST,        // 压力测试（10+个实例）
    MEMORY_STRESS,      // 内存压力测试
    CPU_STRESS          // CPU压力测试
};

class PerformanceBenchmark {
private:
    PerformanceMetrics currentMetrics;
    std::map<TestScenario, PerformanceMetrics> benchmarkResults;
    std::chrono::high_resolution_clock::time_point startTime;
    HANDLE hProcess;
    bool isMonitoring;

public:
    PerformanceBenchmark();
    ~PerformanceBenchmark();

    // 开始性能监控
    bool StartMonitoring();

    // 停止性能监控
    bool StopMonitoring();

    // 执行基准测试
    bool RunBenchmark(TestScenario scenario, const std::string& testApp);

    // 获取当前性能指标
    PerformanceMetrics GetCurrentMetrics();

    // 记录时间点
    void MarkTimePoint(const std::string& eventName);

    // 更新内存使用情况
    void UpdateMemoryUsage();

    // 更新CPU使用情况
    void UpdateCPUUsage();

    // 生成性能报告
    std::string GeneratePerformanceReport();

    // 导出基准测试结果
    bool ExportBenchmarkResults(const std::string& filePath);

    // 比较性能指标
    std::string CompareMetrics(const PerformanceMetrics& baseline,
                              const PerformanceMetrics& current);

private:
    // 获取进程内存信息
    bool GetProcessMemoryInfo(PROCESS_MEMORY_COUNTERS& memInfo);

    // 获取系统CPU使用率
    double GetSystemCPUUsage();

    // 获取进程CPU使用率
    double GetProcessCPUUsage();

    // 计算时间差
    DWORD CalculateTimeDifference(const std::chrono::high_resolution_clock::time_point& start);

    // 内存泄漏检测
    bool DetectMemoryLeaks();

    // 性能瓶颈分析
    std::vector<std::string> AnalyzeBottlenecks();
};

#endif
```

### 基准测试标准

#### 内存使用标准
```
优秀: 基础内存 < 50MB, 峰值内存 < 200MB
良好: 基础内存 < 100MB, 峰值内存 < 500MB
一般: 基础内存 < 200MB, 峰值内存 < 1GB
需优化: 基础内存 > 200MB 或 峰值内存 > 1GB
```

#### CPU使用标准
```
优秀: 空闲 < 1%, 分析 < 30%, 启动 < 50%
良好: 空闲 < 3%, 分析 < 50%, 启动 < 70%
一般: 空闲 < 5%, 分析 < 70%, 启动 < 90%
需优化: 空闲 > 5% 或 分析 > 70% 或 启动 > 90%
```

#### 响应时间标准
```
优秀: 界面响应 < 100ms, 分析 < 5s, 启动 < 3s
良好: 界面响应 < 200ms, 分析 < 10s, 启动 < 5s
一般: 界面响应 < 500ms, 分析 < 20s, 启动 < 10s
需优化: 界面响应 > 500ms 或 分析 > 20s 或 启动 > 10s
```

## 使用说明

### 命令行用法
```batch
# 基本用法
MultiLauncher.exe "C:\Program Files\TestApp\app.exe"

# 启动多个实例
MultiLauncher.exe "C:\Program Files\TestApp\app.exe" 3

# 分析模式（仅分析不启动）
MultiLauncher.exe --analyze "C:\Program Files\TestApp\app.exe"

# 详细输出
MultiLauncher.exe --verbose "C:\Program Files\TestApp\app.exe"

# 保存分析结果
MultiLauncher.exe --save-profile "C:\Program Files\TestApp\app.exe"

# 批量处理模式
MultiLauncher.exe --batch "config\batch_config.json"

# 性能基准测试
MultiLauncher.exe --benchmark --scenario=stress "C:\Program Files\TestApp\app.exe"

# 静默模式（无GUI）
MultiLauncher.exe --silent --instances=5 "C:\Program Files\TestApp\app.exe"
```

## 常见商业软件绕过方法

### Office系列软件

#### Microsoft Word
```json
{
  "name": "Microsoft Word",
  "executable": "WINWORD.EXE",
  "version_range": "2016-2021",
  "restrictions": ["MUTEX_LOCK", "FILE_LOCK", "REGISTRY_CHECK"],
  "known_mutexes": [
    "_MSO_DW_{PID}",
    "OfficeClickToRunSvc",
    "WinWordStartupMutex"
  ],
  "bypass_strategies": [
    {
      "type": "MUTEX_LOCK",
      "method": "API_HOOK",
      "dll": "office_mutex_hook.dll",
      "success_rate": 95,
      "notes": "Hook CreateMutex系列API，动态修改Mutex名称"
    },
    {
      "type": "FILE_LOCK",
      "method": "FILE_REDIRECT",
      "target_dir": "%APPDATA%\\Microsoft\\Word\\MultiInstance\\{PID}",
      "files_to_redirect": [
        "Normal.dotm",
        "Recent.dat",
        "Word.qat"
      ],
      "success_rate": 90
    },
    {
      "type": "REGISTRY_CHECK",
      "method": "REGISTRY_VIRTUALIZATION",
      "base_key": "HKCU\\Software\\Microsoft\\Office\\16.0\\Word",
      "success_rate": 85
    }
  ],
  "special_notes": "需要处理COM组件注册和DDE通信"
}
```

#### Microsoft Excel
```json
{
  "name": "Microsoft Excel",
  "executable": "EXCEL.EXE",
  "version_range": "2016-2021",
  "restrictions": ["MUTEX_LOCK", "FILE_LOCK", "COM_REGISTRATION"],
  "known_mutexes": [
    "_MSO_DW_{PID}",
    "ExcelStartupMutex",
    "ExcelDDEMutex"
  ],
  "bypass_strategies": [
    {
      "type": "MUTEX_LOCK",
      "method": "API_HOOK",
      "dll": "office_mutex_hook.dll",
      "success_rate": 93
    },
    {
      "type": "COM_REGISTRATION",
      "method": "COM_ISOLATION",
      "isolated_classes": [
        "Excel.Application",
        "Excel.Sheet",
        "Excel.Chart"
      ],
      "success_rate": 88
    }
  ],
  "launch_parameters": "/automation -Embedding",
  "special_notes": "Excel的COM自动化接口需要特殊处理"
}
```

### Adobe系列软件

#### Adobe Photoshop
```json
{
  "name": "Adobe Photoshop",
  "executable": "Photoshop.exe",
  "version_range": "CC 2019-2023",
  "restrictions": ["MUTEX_LOCK", "SHARED_MEMORY", "HARDWARE_ID", "LICENSE_CHECK"],
  "known_mutexes": [
    "PhotoshopSingleInstance",
    "AdobeCreativeCloudMutex",
    "PSAutoRecover"
  ],
  "bypass_strategies": [
    {
      "type": "MUTEX_LOCK",
      "method": "API_HOOK",
      "dll": "adobe_mutex_hook.dll",
      "success_rate": 85,
      "notes": "Adobe使用多层Mutex保护"
    },
    {
      "type": "SHARED_MEMORY",
      "method": "MEMORY_ISOLATION",
      "shared_sections": [
        "AdobeIPCMemory",
        "PhotoshopSharedData"
      ],
      "success_rate": 80
    },
    {
      "type": "HARDWARE_ID",
      "method": "HARDWARE_SPOOFING",
      "spoof_targets": ["MAC_ADDRESS", "CPU_ID", "MOTHERBOARD_ID"],
      "success_rate": 75
    },
    {
      "type": "LICENSE_CHECK",
      "method": "LICENSE_BYPASS",
      "bypass_dll": "adobe_license_hook.dll",
      "success_rate": 70,
      "legal_warning": "仅用于测试目的，请确保拥有合法许可证"
    }
  ],
  "special_requirements": [
    "需要管理员权限",
    "可能触发Adobe Creative Cloud检测",
    "建议在虚拟机中测试"
  ]
}
```

#### Adobe Acrobat Reader
```json
{
  "name": "Adobe Acrobat Reader",
  "executable": "AcroRd32.exe",
  "version_range": "DC 2020-2023",
  "restrictions": ["MUTEX_LOCK", "FILE_LOCK", "PROCESS_CHECK"],
  "bypass_strategies": [
    {
      "type": "MUTEX_LOCK",
      "method": "API_HOOK",
      "dll": "acrobat_hook.dll",
      "success_rate": 92
    },
    {
      "type": "PROCESS_CHECK",
      "method": "PROCESS_HIDING",
      "hide_from_apis": ["CreateToolhelp32Snapshot", "EnumProcesses"],
      "success_rate": 88
    }
  ]
}
```

### 游戏和娱乐软件

#### Steam客户端
```json
{
  "name": "Steam Client",
  "executable": "steam.exe",
  "restrictions": ["MUTEX_LOCK", "NETWORK_PORT", "REGISTRY_CHECK"],
  "known_mutexes": ["SteamClientMutex", "SteamAppMutex"],
  "bypass_strategies": [
    {
      "type": "NETWORK_PORT",
      "method": "PORT_VIRTUALIZATION",
      "default_ports": [27015, 27016, 27017],
      "success_rate": 90
    },
    {
      "type": "REGISTRY_CHECK",
      "method": "REGISTRY_REDIRECTION",
      "base_key": "HKCU\\Software\\Valve\\Steam",
      "success_rate": 85
    }
  ],
  "launch_parameters": "-no-browser +clientport {PORT}",
  "special_notes": "每个实例需要不同的客户端端口"
}
```

### 开发工具

#### Visual Studio Code
```json
{
  "name": "Visual Studio Code",
  "executable": "Code.exe",
  "restrictions": ["MUTEX_LOCK", "FILE_LOCK", "NAMED_PIPE"],
  "bypass_strategies": [
    {
      "type": "NAMED_PIPE",
      "method": "PIPE_REDIRECTION",
      "pipe_names": ["vscode-ipc", "vscode-git"],
      "success_rate": 95
    }
  ],
  "launch_parameters": "--user-data-dir=\"%TEMP%\\VSCode\\{PID}\"",
  "success_rate": 98,
  "notes": "VSCode支持多实例，主要通过用户数据目录隔离"
}
```

### 配置文件详细格式

#### 主配置文件 (config/main_config.json)
```json
{
  "application": {
    "name": "万能多开器",
    "version": "1.0.0",
    "build": "20231201",
    "debug_mode": false
  },
  "detection": {
    "enable_static_analysis": true,
    "enable_dynamic_analysis": true,
    "enable_ml_classification": true,
    "enable_rule_engine": true,
    "confidence_threshold": 0.7,
    "fallback_threshold": 0.5,
    "max_analysis_time": 30000,
    "analysis_timeout": 60000
  },
  "bypass": {
    "default_strategy": "AUTO",
    "max_instances": 10,
    "instance_delay": 1000,
    "cleanup_on_exit": true,
    "stealth_mode": true,
    "anti_detection": true
  },
  "gui": {
    "theme": "auto",
    "language": "zh-CN",
    "window_size": {
      "width": 800,
      "height": 600,
      "min_width": 600,
      "min_height": 400
    },
    "auto_analyze": true,
    "show_advanced_options": false,
    "log_level": "INFO"
  },
  "security": {
    "require_admin": false,
    "verify_signatures": true,
    "sandbox_mode": false,
    "whitelist_only": false,
    "max_file_size": 104857600
  },
  "performance": {
    "max_memory_usage": 1073741824,
    "cpu_limit": 80,
    "enable_monitoring": true,
    "auto_cleanup": true,
    "cache_analysis_results": true,
    "cache_expiry": 86400
  },
  "logging": {
    "enable_file_logging": true,
    "log_file_path": "logs/multilauncher.log",
    "max_log_size": 10485760,
    "log_rotation": true,
    "log_level": "INFO",
    "enable_debug_logging": false
  },
  "updates": {
    "check_for_updates": true,
    "auto_update": false,
    "update_channel": "stable",
    "update_server": "https://updates.example.com"
  }
}
```

#### 应用程序配置文件 (config/app_profiles.json)
```json
{
  "version": "1.0",
  "last_updated": "2023-12-01T10:00:00Z",
  "profiles": [
    {
      "id": "test_app_001",
      "name": "测试应用程序",
      "executable": "testapp.exe",
      "version_range": "1.0-2.0",
      "publisher": "Test Company",
      "description": "用于测试的示例应用程序",
      "category": "测试工具",
      "restrictions": [
        {
          "type": "MUTEX_LOCK",
          "confidence": 0.95,
          "evidence": ["CreateMutexA", "OpenMutexA"],
          "mutex_names": ["TestAppMutex", "TestApp_SingleInstance"]
        },
        {
          "type": "FILE_LOCK",
          "confidence": 0.87,
          "evidence": ["CreateFileA with GENERIC_WRITE"],
          "locked_files": ["config.ini", "app.lock"]
        }
      ],
      "bypass_strategies": [
        {
          "id": "strategy_001",
          "type": "MUTEX_LOCK",
          "method": "API_HOOK",
          "priority": 1,
          "success_rate": 0.95,
          "dll": "mutex_hook.dll",
          "parameters": {
            "hook_functions": ["CreateMutexA", "CreateMutexW", "OpenMutexA", "OpenMutexW"],
            "name_modification": "append_pid",
            "stealth_mode": true
          },
          "requirements": ["admin_rights"],
          "compatibility": ["Windows 7", "Windows 10", "Windows 11"],
          "notes": "标准Mutex Hook策略，适用于大多数应用"
        },
        {
          "id": "strategy_002",
          "type": "FILE_LOCK",
          "method": "FILE_REDIRECT",
          "priority": 2,
          "success_rate": 0.90,
          "parameters": {
            "redirect_base": "%TEMP%\\MultiLauncher\\{APP_NAME}\\{PID}",
            "files_to_redirect": ["config.ini", "app.lock", "temp.dat"],
            "copy_original": true,
            "cleanup_on_exit": true
          },
          "notes": "将锁定文件重定向到临时目录"
        }
      ],
      "launch_parameters": {
        "default": "",
        "silent": "/silent",
        "windowed": "/windowed",
        "custom": "/instance:{INSTANCE_ID}"
      },
      "environment_variables": {
        "APP_INSTANCE_ID": "{PID}",
        "APP_TEMP_DIR": "%TEMP%\\MultiLauncher\\{PID}"
      },
      "post_launch_actions": [
        {
          "action": "wait_for_window",
          "parameters": {
            "window_class": "TestAppMainWindow",
            "timeout": 10000
          }
        },
        {
          "action": "inject_dll",
          "parameters": {
            "dll_path": "hooks\\runtime_hook.dll",
            "injection_method": "CreateRemoteThread"
          }
        }
      ],
      "known_issues": [
        {
          "issue": "在Windows 7上可能需要额外的权限",
          "severity": "medium",
          "workaround": "以管理员身份运行"
        }
      ],
      "testing_info": {
        "last_tested": "2023-11-30",
        "tested_versions": ["1.0", "1.5", "2.0"],
        "test_results": {
          "success_rate": 0.92,
          "average_launch_time": 2500,
          "max_instances_tested": 5
        }
      }
    }
  ],
  "global_settings": {
    "default_instance_limit": 5,
    "default_timeout": 30000,
    "enable_auto_detection": true,
    "fallback_strategies": ["PROCESS_ISOLATION", "SANDBOX_MODE"]
  }
}
```

#### 规则引擎配置 (config/detection_rules.json)
```json
{
  "version": "1.0",
  "rules": [
    {
      "id": "rule_mutex_001",
      "name": "标准Mutex检测",
      "description": "检测使用CreateMutex API的单实例限制",
      "category": "MUTEX_DETECTION",
      "priority": 10,
      "enabled": true,
      "conditions": [
        {
          "type": "API_IMPORT",
          "pattern": "CreateMutex[AW]",
          "weight": 0.8,
          "required": true
        },
        {
          "type": "STRING_PATTERN",
          "pattern": ".*Mutex.*",
          "weight": 0.3,
          "required": false
        },
        {
          "type": "STRING_PATTERN",
          "pattern": ".*SingleInstance.*",
          "weight": 0.5,
          "required": false
        }
      ],
      "threshold": 0.7,
      "output": {
        "restriction_type": "MUTEX_LOCK",
        "confidence_base": 0.85,
        "recommended_strategies": ["API_HOOK", "MUTEX_RENAME"]
      }
    },
    {
      "id": "rule_file_001",
      "name": "文件锁定检测",
      "description": "检测独占文件访问限制",
      "category": "FILE_DETECTION",
      "priority": 8,
      "enabled": true,
      "conditions": [
        {
          "type": "API_IMPORT",
          "pattern": "CreateFile[AW]",
          "weight": 0.6,
          "required": true
        },
        {
          "type": "STRING_PATTERN",
          "pattern": ".*\\.lock",
          "weight": 0.4,
          "required": false
        },
        {
          "type": "BEHAVIOR_PATTERN",
          "pattern": "exclusive_file_access",
          "weight": 0.7,
          "required": false
        }
      ],
      "threshold": 0.6,
      "output": {
        "restriction_type": "FILE_LOCK",
        "confidence_base": 0.75,
        "recommended_strategies": ["FILE_REDIRECT", "FILE_VIRTUALIZATION"]
      }
    }
  ],
  "global_settings": {
    "enable_machine_learning": true,
    "ml_weight": 0.4,
    "rule_weight": 0.6,
    "min_confidence": 0.5,
    "max_rules_per_scan": 50
  }
}
```

## 调试指南扩展

### 专业调试工具集

#### 1. 系统监控工具
- **Process Monitor (ProcMon)** - 实时监控文件系统、注册表和进程/线程活动
  - 过滤器设置：进程名、操作类型、路径模式
  - 导出功能：CSV、XML格式，便于后续分析
  - 高级功能：堆栈跟踪、进程树视图

- **API Monitor** - 监控和显示API调用
  - 支持32位和64位进程
  - 可自定义API监控列表
  - 实时显示参数和返回值
  - 断点和条件监控功能

- **Dependency Walker** - 分析PE文件的DLL依赖关系
  - 检测缺失的DLL
  - 分析导入/导出函数
  - 识别循环依赖问题

#### 2. 动态调试工具
- **x64dbg/x32dbg** - 现代化的Windows调试器
  - 插件系统支持
  - 脚本自动化功能
  - 内存搜索和补丁功能
  - 反汇编和十六进制编辑

- **Cheat Engine** - 内存扫描和修改工具
  - 内存搜索和过滤
  - 代码注入和Hook
  - 调试器功能
  - Lua脚本支持

- **WinAPIOverride** - API监控和重定向工具
  - 实时API监控
  - 参数修改和返回值伪造
  - 自定义DLL注入
  - 日志记录和分析

#### 3. 网络和通信调试
- **Wireshark** - 网络协议分析器
  - 捕获和分析网络流量
  - 过滤和搜索功能
  - 协议解析和统计

- **TCPView** - 显示网络连接和端口使用情况
  - 实时监控TCP/UDP连接
  - 进程关联显示
  - 连接状态跟踪

### 复杂场景调试方案

#### 场景1：加壳程序调试
```cpp
// 加壳检测和处理
class PackerDetector {
public:
    enum PackerType {
        UPX,
        ASPACK,
        PECOMPACT,
        THEMIDA,
        VMPROTECT,
        UNKNOWN
    };

    PackerType DetectPacker(const std::string& exePath) {
        // 1. 检查PE节名称
        std::vector<std::string> sectionNames = GetSectionNames(exePath);

        // UPX特征
        if (std::find(sectionNames.begin(), sectionNames.end(), "UPX0") != sectionNames.end()) {
            return UPX;
        }

        // ASPack特征
        if (std::find(sectionNames.begin(), sectionNames.end(), ".aspack") != sectionNames.end()) {
            return ASPACK;
        }

        // 2. 检查导入表特征
        std::vector<std::string> imports = GetImportFunctions(exePath);
        if (imports.size() < 10) {
            // 导入函数过少，可能被加壳
            return UNKNOWN;
        }

        // 3. 检查入口点代码
        std::vector<BYTE> entryCode = GetEntryPointCode(exePath);
        if (IsPackedCode(entryCode)) {
            return UNKNOWN;
        }

        return UNKNOWN;
    }

    bool UnpackProgram(const std::string& exePath, PackerType type) {
        switch (type) {
            case UPX:
                return UnpackUPX(exePath);
            case ASPACK:
                return UnpackASPack(exePath);
            default:
                return GenericUnpack(exePath);
        }
    }

private:
    bool UnpackUPX(const std::string& exePath) {
        // 使用UPX命令行工具解包
        std::string command = "upx -d \"" + exePath + "\"";
        return system(command.c_str()) == 0;
    }

    bool GenericUnpack(const std::string& exePath) {
        // 通用脱壳方法：内存dump
        DWORD processId = LaunchSuspended(exePath);
        if (processId == 0) return false;

        // 等待OEP到达
        PVOID oep = WaitForOEP(processId);
        if (oep == nullptr) {
            TerminateProcess(processId);
            return false;
        }

        // Dump内存到文件
        bool success = DumpProcessMemory(processId, exePath + ".unpacked");
        TerminateProcess(processId);

        return success;
    }
};
```

#### 场景2：反调试程序处理
```cpp
// 反调试检测和绕过
class AntiDebugHandler {
public:
    struct AntiDebugInfo {
        std::vector<std::string> detectedTechniques;
        std::vector<PVOID> patchAddresses;
        bool isProtected;
        std::string protectionLevel;
    };

    AntiDebugInfo AnalyzeAntiDebug(const std::string& exePath) {
        AntiDebugInfo info;

        // 1. 静态分析反调试API
        std::vector<std::string> antiDebugAPIs = {
            "IsDebuggerPresent",
            "CheckRemoteDebuggerPresent",
            "NtQueryInformationProcess",
            "GetTickCount",
            "QueryPerformanceCounter",
            "OutputDebugStringA"
        };

        for (const auto& api : antiDebugAPIs) {
            if (HasImport(exePath, api)) {
                info.detectedTechniques.push_back("API: " + api);
            }
        }

        // 2. 检测反调试字符串
        std::vector<std::string> antiDebugStrings = {
            "debugger",
            "ollydbg",
            "x64dbg",
            "ida",
            "windbg"
        };

        for (const auto& str : antiDebugStrings) {
            if (HasString(exePath, str)) {
                info.detectedTechniques.push_back("String: " + str);
            }
        }

        // 3. 动态分析
        DWORD processId = LaunchForAnalysis(exePath);
        if (processId != 0) {
            // 监控反调试行为
            MonitorAntiDebugBehavior(processId, info);
            TerminateProcess(processId);
        }

        // 评估保护级别
        if (info.detectedTechniques.size() > 10) {
            info.protectionLevel = "高";
        } else if (info.detectedTechniques.size() > 5) {
            info.protectionLevel = "中";
        } else {
            info.protectionLevel = "低";
        }

        info.isProtected = !info.detectedTechniques.empty();

        return info;
    }

    bool BypassAntiDebug(const std::string& exePath, const AntiDebugInfo& info) {
        // 1. 创建补丁文件
        std::string patchedPath = exePath + ".patched";
        if (!CopyFileA(exePath.c_str(), patchedPath.c_str(), FALSE)) {
            return false;
        }

        // 2. 应用补丁
        for (const auto& technique : info.detectedTechniques) {
            if (technique.find("IsDebuggerPresent") != std::string::npos) {
                PatchIsDebuggerPresent(patchedPath);
            } else if (technique.find("NtQueryInformationProcess") != std::string::npos) {
                PatchNtQueryInformationProcess(patchedPath);
            }
            // ... 其他补丁
        }

        return true;
    }

private:
    bool PatchIsDebuggerPresent(const std::string& filePath) {
        // 将IsDebuggerPresent调用替换为返回FALSE
        // 搜索调用模式：FF 15 xx xx xx xx (call dword ptr [xxxxxxxx])
        std::vector<BYTE> pattern = {0xFF, 0x15};
        std::vector<BYTE> replacement = {0x33, 0xC0, 0x90, 0x90, 0x90, 0x90}; // xor eax,eax; nop*4

        return PatchBinaryFile(filePath, pattern, replacement);
    }

    bool PatchBinaryFile(const std::string& filePath,
                        const std::vector<BYTE>& pattern,
                        const std::vector<BYTE>& replacement) {
        // 实现二进制文件补丁功能
        HANDLE hFile = CreateFileA(filePath.c_str(), GENERIC_READ | GENERIC_WRITE,
                                  0, NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
        if (hFile == INVALID_HANDLE_VALUE) return false;

        DWORD fileSize = GetFileSize(hFile, NULL);
        std::vector<BYTE> fileData(fileSize);

        DWORD bytesRead;
        ReadFile(hFile, fileData.data(), fileSize, &bytesRead, NULL);

        // 搜索和替换模式
        bool patched = false;
        for (size_t i = 0; i <= fileData.size() - pattern.size(); i++) {
            if (std::equal(pattern.begin(), pattern.end(), fileData.begin() + i)) {
                std::copy(replacement.begin(), replacement.end(), fileData.begin() + i);
                patched = true;
            }
        }

        if (patched) {
            SetFilePointer(hFile, 0, NULL, FILE_BEGIN);
            DWORD bytesWritten;
            WriteFile(hFile, fileData.data(), fileSize, &bytesWritten, NULL);
        }

        CloseHandle(hFile);
        return patched;
    }
};
```

#### 场景3：虚拟化保护程序
```cpp
// 虚拟化保护检测和处理
class VirtualizationHandler {
public:
    enum VirtualizationType {
        VMPROTECT,
        THEMIDA,
        ENIGMA,
        CODE_VIRTUALIZER,
        UNKNOWN_VM
    };

    VirtualizationType DetectVirtualization(const std::string& exePath) {
        // 1. 检查特征字符串
        if (HasString(exePath, "VMProtect")) return VMPROTECT;
        if (HasString(exePath, "Themida")) return THEMIDA;
        if (HasString(exePath, "Enigma")) return ENIGMA;

        // 2. 检查代码特征
        std::vector<BYTE> entryCode = GetEntryPointCode(exePath);
        if (IsVMProtectCode(entryCode)) return VMPROTECT;
        if (IsThemidaCode(entryCode)) return THEMIDA;

        // 3. 检查节结构
        std::vector<std::string> sections = GetSectionNames(exePath);
        for (const auto& section : sections) {
            if (section.find(".vmp") != std::string::npos) return VMPROTECT;
            if (section.find(".tmd") != std::string::npos) return THEMIDA;
        }

        return UNKNOWN_VM;
    }

    bool HandleVirtualizedProgram(const std::string& exePath, VirtualizationType type) {
        switch (type) {
            case VMPROTECT:
                return HandleVMProtect(exePath);
            case THEMIDA:
                return HandleThemida(exePath);
            default:
                return HandleGenericVM(exePath);
        }
    }

private:
    bool HandleVMProtect(const std::string& exePath) {
        // VMProtect特殊处理
        // 1. 使用内存断点跟踪
        // 2. 识别VM入口和出口
        // 3. 重建原始代码
        return true;
    }

    bool HandleGenericVM(const std::string& exePath) {
        // 通用虚拟化处理
        // 1. 动态分析VM行为
        // 2. 记录指令执行轨迹
        // 3. 尝试代码重建
        return true;
    }
};
```

### 异常恢复方案

#### 内存泄漏检测和修复
```cpp
// 内存泄漏检测器
class MemoryLeakDetector {
private:
    struct AllocationInfo {
        PVOID address;
        SIZE_T size;
        std::string source;
        DWORD timestamp;
        std::vector<PVOID> callStack;
    };

    std::map<PVOID, AllocationInfo> allocations;
    CRITICAL_SECTION cs;
    bool isEnabled;

public:
    MemoryLeakDetector() : isEnabled(false) {
        InitializeCriticalSection(&cs);
    }

    ~MemoryLeakDetector() {
        DeleteCriticalSection(&cs);
    }

    void Enable() {
        EnterCriticalSection(&cs);
        isEnabled = true;

        // Hook内存分配函数
        HookMemoryFunctions();

        LeaveCriticalSection(&cs);
    }

    void Disable() {
        EnterCriticalSection(&cs);
        isEnabled = false;

        // 恢复原始函数
        UnhookMemoryFunctions();

        LeaveCriticalSection(&cs);
    }

    void RecordAllocation(PVOID address, SIZE_T size, const std::string& source) {
        if (!isEnabled) return;

        EnterCriticalSection(&cs);

        AllocationInfo info;
        info.address = address;
        info.size = size;
        info.source = source;
        info.timestamp = GetTickCount();
        info.callStack = CaptureCallStack();

        allocations[address] = info;

        LeaveCriticalSection(&cs);
    }

    void RecordDeallocation(PVOID address) {
        if (!isEnabled) return;

        EnterCriticalSection(&cs);
        allocations.erase(address);
        LeaveCriticalSection(&cs);
    }

    std::vector<AllocationInfo> DetectLeaks() {
        std::vector<AllocationInfo> leaks;

        EnterCriticalSection(&cs);

        DWORD currentTime = GetTickCount();
        for (const auto& pair : allocations) {
            const AllocationInfo& info = pair.second;

            // 超过5分钟未释放的内存视为泄漏
            if (currentTime - info.timestamp > 300000) {
                leaks.push_back(info);
            }
        }

        LeaveCriticalSection(&cs);

        return leaks;
    }

    bool FixLeaks() {
        std::vector<AllocationInfo> leaks = DetectLeaks();

        for (const auto& leak : leaks) {
            // 尝试释放泄漏的内存
            if (IsSafeToFree(leak.address)) {
                free(leak.address);
                RecordDeallocation(leak.address);
            }
        }

        return true;
    }

private:
    std::vector<PVOID> CaptureCallStack() {
        std::vector<PVOID> callStack;

        // 使用CaptureStackBackTrace捕获调用栈
        PVOID stack[16];
        USHORT frames = CaptureStackBackTrace(0, 16, stack, NULL);

        for (USHORT i = 0; i < frames; i++) {
            callStack.push_back(stack[i]);
        }

        return callStack;
    }

    bool IsSafeToFree(PVOID address) {
        // 检查内存是否可以安全释放
        MEMORY_BASIC_INFORMATION mbi;
        if (VirtualQuery(address, &mbi, sizeof(mbi)) == 0) {
            return false;
        }

        // 检查内存状态
        return (mbi.State == MEM_COMMIT) &&
               (mbi.Type == MEM_PRIVATE) &&
               (mbi.Protect & PAGE_READWRITE);
    }
};
```

### 常见问题解决方案

#### 1. DLL注入失败的完整诊断
```cpp
// DLL注入失败诊断器
class InjectionDiagnostic {
public:
    enum FailureReason {
        PROCESS_NOT_FOUND,
        INSUFFICIENT_PRIVILEGES,
        ARCHITECTURE_MISMATCH,
        DLL_NOT_FOUND,
        DLL_LOAD_FAILED,
        REMOTE_THREAD_FAILED,
        HOOK_DETECTION,
        ANTIVIRUS_BLOCKED,
        UNKNOWN_ERROR
    };

    FailureReason DiagnoseInjectionFailure(DWORD processId, const std::string& dllPath) {
        // 1. 检查进程是否存在
        HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, processId);
        if (hProcess == NULL) {
            return PROCESS_NOT_FOUND;
        }

        // 2. 检查权限
        HANDLE hProcessFull = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (hProcessFull == NULL) {
            CloseHandle(hProcess);
            return INSUFFICIENT_PRIVILEGES;
        }

        // 3. 检查架构匹配
        BOOL isWow64Process, isWow64Current;
        IsWow64Process(hProcess, &isWow64Process);
        IsWow64Process(GetCurrentProcess(), &isWow64Current);

        if (isWow64Process != isWow64Current) {
            CloseHandle(hProcess);
            CloseHandle(hProcessFull);
            return ARCHITECTURE_MISMATCH;
        }

        // 4. 检查DLL文件
        if (GetFileAttributesA(dllPath.c_str()) == INVALID_FILE_ATTRIBUTES) {
            CloseHandle(hProcess);
            CloseHandle(hProcessFull);
            return DLL_NOT_FOUND;
        }

        // 5. 尝试在目标进程中加载DLL
        PVOID remotePath = VirtualAllocEx(hProcessFull, NULL, dllPath.length() + 1,
                                         MEM_COMMIT, PAGE_READWRITE);
        if (remotePath == NULL) {
            CloseHandle(hProcess);
            CloseHandle(hProcessFull);
            return UNKNOWN_ERROR;
        }

        WriteProcessMemory(hProcessFull, remotePath, dllPath.c_str(),
                          dllPath.length() + 1, NULL);

        HANDLE hThread = CreateRemoteThread(hProcessFull, NULL, 0,
                                           (LPTHREAD_START_ROUTINE)LoadLibraryA,
                                           remotePath, 0, NULL);

        if (hThread == NULL) {
            VirtualFreeEx(hProcessFull, remotePath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            CloseHandle(hProcessFull);
            return REMOTE_THREAD_FAILED;
        }

        // 等待加载完成
        WaitForSingleObject(hThread, 5000);

        DWORD exitCode;
        GetExitCodeThread(hThread, &exitCode);

        CloseHandle(hThread);
        VirtualFreeEx(hProcessFull, remotePath, 0, MEM_RELEASE);
        CloseHandle(hProcess);
        CloseHandle(hProcessFull);

        if (exitCode == 0) {
            return DLL_LOAD_FAILED;
        }

        return UNKNOWN_ERROR;
    }

    std::string GetFailureDescription(FailureReason reason) {
        switch (reason) {
            case PROCESS_NOT_FOUND:
                return "目标进程不存在或已退出";
            case INSUFFICIENT_PRIVILEGES:
                return "权限不足，请以管理员身份运行";
            case ARCHITECTURE_MISMATCH:
                return "架构不匹配（32位/64位）";
            case DLL_NOT_FOUND:
                return "DLL文件不存在";
            case DLL_LOAD_FAILED:
                return "DLL加载失败，可能缺少依赖";
            case REMOTE_THREAD_FAILED:
                return "远程线程创建失败";
            case HOOK_DETECTION:
                return "目标程序检测到Hook尝试";
            case ANTIVIRUS_BLOCKED:
                return "被杀毒软件阻止";
            default:
                return "未知错误";
        }
    }
};
```

#### 2. Hook失败的详细分析
```cpp
// Hook失败分析器
class HookDiagnostic {
public:
    enum HookFailureReason {
        API_NOT_FOUND,
        ALREADY_HOOKED,
        MEMORY_PROTECTION,
        INSUFFICIENT_SPACE,
        INVALID_INSTRUCTION,
        ANTIHOOK_PROTECTION,
        UNKNOWN_HOOK_ERROR
    };

    HookFailureReason DiagnoseHookFailure(const std::string& moduleName,
                                         const std::string& functionName) {
        // 1. 检查API是否存在
        HMODULE hModule = GetModuleHandleA(moduleName.c_str());
        if (hModule == NULL) {
            hModule = LoadLibraryA(moduleName.c_str());
            if (hModule == NULL) {
                return API_NOT_FOUND;
            }
        }

        FARPROC apiAddress = GetProcAddress(hModule, functionName.c_str());
        if (apiAddress == NULL) {
            return API_NOT_FOUND;
        }

        // 2. 检查是否已被Hook
        if (IsAlreadyHooked(apiAddress)) {
            return ALREADY_HOOKED;
        }

        // 3. 检查内存保护
        MEMORY_BASIC_INFORMATION mbi;
        VirtualQuery(apiAddress, &mbi, sizeof(mbi));

        if (!(mbi.Protect & PAGE_EXECUTE_READWRITE) &&
            !(mbi.Protect & PAGE_EXECUTE_WRITECOPY)) {
            return MEMORY_PROTECTION;
        }

        // 4. 检查指令空间
        if (!HasSufficientSpace(apiAddress)) {
            return INSUFFICIENT_SPACE;
        }

        // 5. 检查指令有效性
        if (!IsValidInstructionSequence(apiAddress)) {
            return INVALID_INSTRUCTION;
        }

        // 6. 检查反Hook保护
        if (HasAntiHookProtection(apiAddress)) {
            return ANTIHOOK_PROTECTION;
        }

        return UNKNOWN_HOOK_ERROR;
    }

private:
    bool IsAlreadyHooked(FARPROC apiAddress) {
        BYTE* code = (BYTE*)apiAddress;

        // 检查常见的Hook特征
        // JMP指令 (E9 xx xx xx xx)
        if (code[0] == 0xE9) return true;

        // PUSH + RET组合
        if (code[0] == 0x68 && code[5] == 0xC3) return true;

        // MOV EAX + JMP EAX
        if (code[0] == 0xB8 && code[5] == 0xFF && code[6] == 0xE0) return true;

        return false;
    }

    bool HasSufficientSpace(FARPROC apiAddress) {
        // 检查是否有足够的空间安装Hook（至少5字节）
        BYTE* code = (BYTE*)apiAddress;

        // 简单检查：确保前5个字节不会破坏指令边界
        // 这里需要更复杂的反汇编逻辑
        return true;
    }

    bool IsValidInstructionSequence(FARPROC apiAddress) {
        // 检查指令序列是否有效
        // 需要反汇编引擎支持
        return true;
    }

    bool HasAntiHookProtection(FARPROC apiAddress) {
        // 检查是否有反Hook保护
        // 例如：代码完整性检查、CRC校验等
        return false;
    }
};
```

#### 3. 权限问题的自动解决
```cpp
// 权限管理器
class PrivilegeManager {
public:
    bool ElevatePrivileges() {
        // 1. 检查当前权限级别
        if (IsRunningAsAdmin()) {
            return true;
        }

        // 2. 尝试获取调试权限
        if (EnableDebugPrivilege()) {
            return true;
        }

        // 3. 请求UAC提升
        return RequestUACElevation();
    }

    bool IsRunningAsAdmin() {
        BOOL isAdmin = FALSE;
        PSID adminGroup = NULL;

        SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;

        if (AllocateAndInitializeSid(&ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                    DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup)) {
            CheckTokenMembership(NULL, adminGroup, &isAdmin);
            FreeSid(adminGroup);
        }

        return isAdmin == TRUE;
    }

    bool EnableDebugPrivilege() {
        HANDLE hToken;
        TOKEN_PRIVILEGES tp;

        if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES, &hToken)) {
            return false;
        }

        tp.PrivilegeCount = 1;
        LookupPrivilegeValue(NULL, SE_DEBUG_NAME, &tp.Privileges[0].Luid);
        tp.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;

        AdjustTokenPrivileges(hToken, FALSE, &tp, sizeof(tp), NULL, NULL);
        CloseHandle(hToken);

        return GetLastError() == ERROR_SUCCESS;
    }

    bool RequestUACElevation() {
        // 重新启动程序并请求管理员权限
        char exePath[MAX_PATH];
        GetModuleFileNameA(NULL, exePath, MAX_PATH);

        SHELLEXECUTEINFOA sei = {0};
        sei.cbSize = sizeof(sei);
        sei.lpVerb = "runas";
        sei.lpFile = exePath;
        sei.hwnd = NULL;
        sei.nShow = SW_NORMAL;

        return ShellExecuteExA(&sei) == TRUE;
    }
};
```

## 性能优化

### 1. 检测优化
- 缓存PE分析结果
- 并行化检测过程
- 智能跳过已知安全的API

### 2. 注入优化
- 预编译Hook DLL
- 使用内存池管理
- 延迟加载非关键模块

### 3. 内存优化
- 及时释放不需要的资源
- 使用智能指针管理内存
- 避免内存泄漏

## 安全考虑

### 1. 代码签名
```batch
# 对编译后的文件进行签名
signtool sign /f certificate.pfx /p password /t http://timestamp.server.com MultiLauncher.exe
```

### 2. 反病毒误报
- 使用白名单技术
- 提交样本到杀毒厂商
- 添加数字签名

### 3. 权限控制
- 最小权限原则
- 用户确认机制
- 审计日志记录

## 扩展开发

### 添加新的检测类型
```cpp
// 1. 在RestrictionType枚举中添加新类型
enum class RestrictionType {
    // ... 现有类型
    NEW_RESTRICTION_TYPE
};

// 2. 在RestrictionDetector中添加检测逻辑
bool DetectNewRestriction(const string& exePath, DetectionResult& result);

// 3. 在BypassEngine中添加绕过策略
bool BypassNewRestriction(const string& exePath, const DetectionResult& detection);
```

### 添加新的绕过方法
```cpp
// 1. 创建新的Hook DLL
// src/hooks/new_hook.cpp

// 2. 在BypassEngine中注册新方法
bool RegisterNewBypassMethod(const string& methodName, BypassFunction func);

// 3. 更新配置文件格式
```

## 完整的部署和分发指南

### 编译和打包流程

#### 1. 预编译准备
```batch
# 创建发布目录结构
mkdir release
mkdir release\bin
mkdir release\hooks
mkdir release\config
mkdir release\docs
mkdir release\logs
mkdir release\temp

# 复制必要的依赖库
copy libs\detours\lib.X64\detours.lib release\bin\
copy libs\sqlite\sqlite3.dll release\bin\
copy "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Redist\MSVC\14.29.30133\x64\Microsoft.VC142.CRT\*.dll" release\bin\
```

#### 2. 自动化编译脚本
```batch
@echo off
echo 万能多开器自动编译脚本
echo ========================

set BUILD_CONFIG=Release
set BUILD_PLATFORM=x64
set VERSION=1.0.0
set BUILD_DATE=%date:~0,4%%date:~5,2%%date:~8,2%

echo 编译配置: %BUILD_CONFIG%
echo 目标平台: %BUILD_PLATFORM%
echo 版本号: %VERSION%
echo 编译日期: %BUILD_DATE%

echo.
echo 1. 清理旧的编译文件...
if exist build rmdir /s /q build
mkdir build

echo 2. 生成项目文件...
cd build
cmake .. -G "Visual Studio 16 2019" -A %BUILD_PLATFORM%
if errorlevel 1 goto error

echo 3. 编译主程序...
cmake --build . --config %BUILD_CONFIG% --target MultiLauncher
if errorlevel 1 goto error

echo 4. 编译Hook DLL...
cmake --build . --config %BUILD_CONFIG% --target mutex_hook
cmake --build . --config %BUILD_CONFIG% --target file_hook
cmake --build . --config %BUILD_CONFIG% --target registry_hook
cmake --build . --config %BUILD_CONFIG% --target window_hook
cmake --build . --config %BUILD_CONFIG% --target process_hook
cmake --build . --config %BUILD_CONFIG% --target memory_hook
cmake --build . --config %BUILD_CONFIG% --target stealth_hook
if errorlevel 1 goto error

echo 5. 运行单元测试...
cmake --build . --config %BUILD_CONFIG% --target RUN_TESTS
if errorlevel 1 echo 警告: 部分测试失败，但继续编译过程

echo 6. 创建发布包...
cd ..
call scripts\create_release_package.bat %VERSION% %BUILD_DATE%
if errorlevel 1 goto error

echo.
echo 编译完成！发布包位于: release\MultiLauncher_%VERSION%_%BUILD_DATE%.zip
goto end

:error
echo.
echo 编译失败！请检查错误信息。
pause
exit /b 1

:end
echo.
echo 按任意键退出...
pause
```

#### 3. 发布包创建脚本
```batch
@echo off
set VERSION=%1
set BUILD_DATE=%2
set PACKAGE_NAME=MultiLauncher_%VERSION%_%BUILD_DATE%

echo 创建发布包: %PACKAGE_NAME%

# 创建临时打包目录
mkdir temp_package
mkdir temp_package\bin
mkdir temp_package\hooks
mkdir temp_package\config
mkdir temp_package\docs
mkdir temp_package\examples

# 复制主程序
copy build\Release\MultiLauncher.exe temp_package\bin\
copy build\Release\*.pdb temp_package\bin\ 2>nul

# 复制Hook DLL
copy build\hooks\Release\*.dll temp_package\hooks\

# 复制配置文件
copy config\*.json temp_package\config\
copy config\*.xml temp_package\config\

# 复制文档
copy docs\*.md temp_package\docs\
copy README.md temp_package\
copy LICENSE temp_package\

# 复制示例程序
copy test_apps\*.exe temp_package\examples\

# 创建版本信息文件
echo Version: %VERSION% > temp_package\VERSION.txt
echo Build Date: %BUILD_DATE% >> temp_package\VERSION.txt
echo Build Configuration: Release >> temp_package\VERSION.txt
echo Target Platform: x64 >> temp_package\VERSION.txt

# 创建安装脚本
echo @echo off > temp_package\install.bat
echo echo 万能多开器安装程序 >> temp_package\install.bat
echo echo ================== >> temp_package\install.bat
echo. >> temp_package\install.bat
echo if not exist "C:\Program Files\MultiLauncher" mkdir "C:\Program Files\MultiLauncher" >> temp_package\install.bat
echo xcopy /s /y bin "C:\Program Files\MultiLauncher\bin\" >> temp_package\install.bat
echo xcopy /s /y hooks "C:\Program Files\MultiLauncher\hooks\" >> temp_package\install.bat
echo xcopy /s /y config "C:\Program Files\MultiLauncher\config\" >> temp_package\install.bat
echo xcopy /s /y docs "C:\Program Files\MultiLauncher\docs\" >> temp_package\install.bat
echo echo 安装完成！ >> temp_package\install.bat
echo pause >> temp_package\install.bat

# 创建卸载脚本
echo @echo off > temp_package\uninstall.bat
echo echo 万能多开器卸载程序 >> temp_package\uninstall.bat
echo echo ================== >> temp_package\uninstall.bat
echo. >> temp_package\uninstall.bat
echo if exist "C:\Program Files\MultiLauncher" rmdir /s /q "C:\Program Files\MultiLauncher" >> temp_package\uninstall.bat
echo echo 卸载完成！ >> temp_package\uninstall.bat
echo pause >> temp_package\uninstall.bat

# 创建ZIP包
powershell Compress-Archive -Path temp_package\* -DestinationPath release\%PACKAGE_NAME%.zip -Force

# 清理临时目录
rmdir /s /q temp_package

echo 发布包创建完成: release\%PACKAGE_NAME%.zip
```

### 数字签名和安全

#### 1. 代码签名流程
```batch
# 使用signtool对所有可执行文件进行签名
set CERT_FILE=certificates\code_signing.pfx
set CERT_PASSWORD=your_password
set TIMESTAMP_SERVER=http://timestamp.digicert.com

echo 对主程序进行数字签名...
signtool sign /f %CERT_FILE% /p %CERT_PASSWORD% /t %TIMESTAMP_SERVER% /d "万能多开器" /du "https://github.com/username/universal-multi-launcher" temp_package\bin\MultiLauncher.exe

echo 对Hook DLL进行数字签名...
for %%f in (temp_package\hooks\*.dll) do (
    signtool sign /f %CERT_FILE% /p %CERT_PASSWORD% /t %TIMESTAMP_SERVER% /d "万能多开器Hook模块" "%%f"
)

echo 验证数字签名...
signtool verify /pa temp_package\bin\MultiLauncher.exe
if errorlevel 1 (
    echo 数字签名验证失败！
    exit /b 1
)

echo 数字签名完成！
```

#### 2. 病毒扫描和白名单提交
```batch
# 自动化病毒扫描脚本
echo 执行病毒扫描...

# 使用Windows Defender扫描
"%ProgramFiles%\Windows Defender\MpCmdRun.exe" -Scan -ScanType 3 -File "temp_package\bin\MultiLauncher.exe"

# 提交到VirusTotal（需要API密钥）
python scripts\virustotal_submit.py temp_package\bin\MultiLauncher.exe

# 生成扫描报告
echo 病毒扫描完成，请查看报告文件。
```

### 分发渠道和更新机制

#### 1. GitHub Releases自动发布
```yaml
# .github/workflows/release.yml
name: Create Release

on:
  push:
    tags:
      - 'v*'

jobs:
  build-and-release:
    runs-on: windows-latest

    steps:
    - uses: actions/checkout@v2

    - name: Setup MSBuild
      uses: microsoft/setup-msbuild@v1

    - name: Setup NuGet
      uses: NuGet/setup-nuget@v1

    - name: Restore dependencies
      run: nuget restore

    - name: Build solution
      run: msbuild /p:Configuration=Release /p:Platform=x64

    - name: Run tests
      run: vstest.console.exe build\Release\Tests.dll

    - name: Create release package
      run: scripts\create_release_package.bat

    - name: Create Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        draft: false
        prerelease: false

    - name: Upload Release Asset
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ./release/MultiLauncher_${{ github.ref }}.zip
        asset_name: MultiLauncher_${{ github.ref }}.zip
        asset_content_type: application/zip
```

#### 2. 自动更新系统
```cpp
// AutoUpdater.h - 自动更新系统
#ifndef AUTOUPDATER_H
#define AUTOUPDATER_H

#include <string>
#include <vector>
#include <functional>

struct UpdateInfo {
    std::string version;
    std::string downloadUrl;
    std::string changelog;
    std::string releaseDate;
    bool isRequired;
    size_t fileSize;
    std::string checksum;
};

class AutoUpdater {
private:
    std::string updateServerUrl;
    std::string currentVersion;
    std::function<void(int)> progressCallback;
    std::function<void(const std::string&)> statusCallback;

public:
    AutoUpdater(const std::string& serverUrl, const std::string& version);
    ~AutoUpdater();

    // 检查更新
    bool CheckForUpdates(UpdateInfo& updateInfo);

    // 下载更新
    bool DownloadUpdate(const UpdateInfo& updateInfo, const std::string& savePath);

    // 安装更新
    bool InstallUpdate(const std::string& updatePath);

    // 设置回调函数
    void SetProgressCallback(std::function<void(int)> callback);
    void SetStatusCallback(std::function<void(const std::string&)> callback);

    // 验证更新文件
    bool VerifyUpdateFile(const std::string& filePath, const std::string& expectedChecksum);

private:
    // HTTP请求
    std::string HttpGet(const std::string& url);
    bool HttpDownload(const std::string& url, const std::string& savePath);

    // 版本比较
    bool IsNewerVersion(const std::string& remoteVersion, const std::string& localVersion);

    // 计算文件校验和
    std::string CalculateChecksum(const std::string& filePath);

    // 解析更新信息
    UpdateInfo ParseUpdateInfo(const std::string& jsonResponse);
};

#endif
```

### 用户手册和帮助文档

#### 1. 快速入门指南
```markdown
# 万能多开器快速入门指南

## 安装步骤

1. **下载程序**
   - 从GitHub Releases页面下载最新版本
   - 或从官方网站下载安装包

2. **解压安装**
   - 解压下载的ZIP文件到任意目录
   - 推荐安装到：C:\Program Files\MultiLauncher\

3. **首次运行**
   - 右键点击MultiLauncher.exe
   - 选择"以管理员身份运行"
   - 首次运行会自动配置必要的权限

## 基本使用

### 方法一：文件选择
1. 点击"浏览"按钮
2. 选择要多开的程序
3. 设置实例数量
4. 点击"启动多开"

### 方法二：拖拽操作
1. 直接拖拽程序文件到窗口
2. 程序会自动识别并填充路径
3. 设置实例数量并启动

## 常见问题

**Q: 程序提示权限不足怎么办？**
A: 请以管理员身份运行程序，或者在程序图标上右键选择"以管理员身份运行"。

**Q: 某些程序无法多开怎么办？**
A: 点击"分析"按钮查看检测结果，程序会自动选择最佳的绕过策略。

**Q: 程序被杀毒软件误报怎么办？**
A: 这是正常现象，请将程序添加到杀毒软件的白名单中。

## 技术支持

如果遇到问题，请：
1. 查看日志文件（logs目录）
2. 在GitHub Issues页面提交问题
3. 发送邮件到技术支持邮箱
```

#### 2. 高级用户配置指南
```markdown
# 高级配置指南

## 自定义配置文件

### 应用程序配置
编辑 `config/app_profiles.json` 文件可以：
- 添加新的应用程序配置
- 修改现有的绕过策略
- 调整检测参数

### 规则引擎配置
编辑 `config/detection_rules.json` 文件可以：
- 添加自定义检测规则
- 调整规则权重和阈值
- 启用/禁用特定规则

## 命令行参数

```batch
# 静默模式运行
MultiLauncher.exe --silent --instances=3 "C:\Program Files\App\app.exe"

# 使用自定义配置文件
MultiLauncher.exe --config="custom_config.json" "app.exe"

# 启用详细日志
MultiLauncher.exe --verbose --log-level=DEBUG "app.exe"
```

## 插件开发

### Hook DLL开发
1. 创建新的DLL项目
2. 实现Hook函数
3. 导出必要的接口
4. 编译并放置到hooks目录

### 检测插件开发
1. 实现IDetectionPlugin接口
2. 注册插件到检测引擎
3. 配置插件参数
```

## 许可证和法律声明

### 使用许可
本项目采用MIT许可证，允许自由使用、修改和分发。但请注意：

1. **合法使用**：本工具仅用于学习、研究和测试目的
2. **禁止滥用**：不得用于破解商业软件的授权机制
3. **责任声明**：使用者需自行承担使用风险和法律责任
4. **商业使用**：商业使用前请咨询法律顾问

### 免责声明
- 本软件按"现状"提供，不提供任何明示或暗示的保证
- 开发者不对使用本软件造成的任何损失承担责任
- 用户应遵守当地法律法规和软件许可协议
- 本软件不得用于任何非法目的

### 第三方组件许可
本项目使用了以下第三方组件：
- Microsoft Detours (MIT License)
- SQLite (Public Domain)
- nlohmann/json (MIT License)
- 其他依赖库的许可证请参见相应的LICENSE文件

## 技术支持和社区

### 官方渠道
- **项目主页**: https://github.com/username/universal-multi-launcher
- **问题反馈**: https://github.com/username/universal-multi-launcher/issues
- **技术文档**: https://github.com/username/universal-multi-launcher/wiki
- **更新日志**: https://github.com/username/universal-multi-launcher/releases

### 社区支持
- **QQ群**: 123456789
- **微信群**: 扫描二维码加入
- **论坛**: https://forum.example.com/multilauncher
- **邮件列表**: <EMAIL>

### 贡献指南
欢迎提交代码贡献：
1. Fork项目到您的GitHub账户
2. 创建功能分支进行开发
3. 提交Pull Request
4. 等待代码审查和合并

### 开发路线图
- **v1.1**: 增加更多商业软件支持
- **v1.2**: 图形界面优化和主题支持
- **v1.3**: 机器学习算法改进
- **v2.0**: 跨平台支持（Linux、macOS）

---

**最后更新**: 2023年12月1日
**文档版本**: 1.0.0
**适用程序版本**: 1.0.0及以上
