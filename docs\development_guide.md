# 万能多开器开发文档

## 项目概述

万能多开器是一个基于C++和Win32 API开发的自动化工具，能够对任何Windows软件实现多开，无需针对特定软件进行定制配置。

### 核心功能
- **通用多开能力** - 支持任何Windows软件（Win32/Win64），包括.NET、Qt、MFC等框架应用
- **自动分析技术** - 自动检测目标软件的多开限制机制（Mutex、文件锁、注册表检查等）
- **自动突破机制** - 根据分析结果自动选择和应用最佳绕过策略，无需用户配置
- **反检测保护** - 实现隐蔽技术防止目标软件检测到多开行为

### 设计理念
- **一键操作** - 用户只需选择程序文件和实例数量，其余全部自动化处理
- **零配置** - 无需复杂的配置文件，程序自动适配各种软件
- **通用性** - 单一工具支持所有类型的Windows应用程序
- **隐蔽性** - 多开过程对目标软件完全透明，不留检测痕迹

## 核心架构

### 三大核心模块

```
MultiLauncher/
├── src/
│   ├── core/
│   │   ├── MultiLauncher.h/cpp        # 主控制器
│   │   ├── AutoDetector.h/cpp         # 自动检测引擎
│   │   ├── AutoBypass.h/cpp           # 自动绕过引擎
│   │   └── StealthModule.h/cpp        # 反检测模块
│   ├── gui/
│   │   ├── SimpleGUI.h/cpp            # 简单界面
│   │   └── FileSelector.h/cpp         # 文件选择
│   ├── hooks/
│   │   ├── mutex_hook.cpp             # Mutex Hook
│   │   ├── file_hook.cpp              # 文件 Hook
│   │   └── registry_hook.cpp          # 注册表 Hook
│   └── main.cpp
├── include/
├── libs/detours/                      # Microsoft Detours
└── build/
```

### 自动化工作流程

```
用户选择程序 → 自动检测限制 → 自动选择策略 → 自动应用绕过 → 启动多个实例
     ↓              ↓              ↓              ↓              ↓
  文件路径      限制类型分析    最佳策略选择    Hook注入应用    多开成功
```

### 核心模块交互

```
MultiLauncher (主控制器)
├── AutoDetector (自动检测引擎)
│   ├── PE分析器
│   ├── 字符串扫描器
│   └── 行为监控器
├── AutoBypass (自动绕过引擎)
│   ├── 策略选择器
│   ├── Hook注入器
│   └── 进程启动器
└── StealthModule (反检测模块)
    ├── DLL隐藏
    ├── Hook隐蔽
    └── 反调试绕过
```

## 开发环境配置

### 必需工具
1. **Visual Studio 2019/2022** - 推荐使用MSVC编译器
2. **Windows SDK 10.0** - 提供Win32 API支持
3. **Microsoft Detours** - API Hook库
4. **Git** - 版本控制

### 快速配置
```bash
# 1. 克隆项目
git clone https://github.com/username/multi-launcher.git
cd multi-launcher

# 2. 下载Detours
git clone https://github.com/Microsoft/Detours.git libs/detours
cd libs/detours && nmake && cd ../..

# 3. 编译项目
mkdir build && cd build
cmake .. -G "Visual Studio 16 2019" -A x64
cmake --build . --config Release
```

### 简化的项目配置
```cmake
# CMakeLists.txt
cmake_minimum_required(VERSION 3.16)
project(MultiLauncher)

set(CMAKE_CXX_STANDARD 17)

# 核心源文件
set(SOURCES
    src/main.cpp
    src/core/MultiLauncher.cpp
    src/core/AutoDetector.cpp
    src/core/AutoBypass.cpp
    src/core/StealthModule.cpp
    src/gui/SimpleGUI.cpp
    src/gui/FileSelector.cpp
)

# 主程序
add_executable(MultiLauncher ${SOURCES})

# 链接库
target_link_libraries(MultiLauncher
    detours psapi advapi32 kernel32 user32 shell32 comctl32
)

# Hook DLL
add_library(mutex_hook SHARED src/hooks/mutex_hook.cpp)
add_library(file_hook SHARED src/hooks/file_hook.cpp)
add_library(registry_hook SHARED src/hooks/registry_hook.cpp)

target_link_libraries(mutex_hook detours)
target_link_libraries(file_hook detours)
target_link_libraries(registry_hook detours)
```

## 简单GUI实现

### 界面设计原则
- **极简设计**: 只保留核心功能，避免复杂操作
- **一键操作**: 用户只需选择文件和设置实例数量
- **自动化**: 所有检测和绕过过程自动完成，无需用户干预

### 简化界面布局
```
┌─────────────────────────────────────────────┐
│ 万能多开器                          [_][×] │
├─────────────────────────────────────────────┤
│ 目标程序: [选择程序文件...] [浏览]          │
│ 实例数量: [3    ▲▼]                        │
│ [启动多开]                                  │
├─────────────────────────────────────────────┤
│ 状态: 就绪                                  │
└─────────────────────────────────────────────┘
```

### 简化GUI实现

```cpp
// SimpleGUI.h - 简化的图形界面
#ifndef SIMPLEGUI_H
#define SIMPLEGUI_H

#include <windows.h>
#include <commdlg.h>
#include <string>

class SimpleGUI {
private:
    HWND hWnd;              // 主窗口
    HWND hFileEdit;         // 文件路径编辑框
    HWND hBrowseBtn;        // 浏览按钮
    HWND hInstanceSpin;     // 实例数量
    HWND hLaunchBtn;        // 启动按钮
    HWND hStatusText;       // 状态显示

public:
    SimpleGUI();
    ~SimpleGUI();

    bool Initialize(HINSTANCE hInstance);
    void Show();
    void UpdateStatus(const std::string& status);

    // 核心事件处理
    void OnBrowseFile();
    void OnLaunch();

    static LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam);

private:
    void CreateControls();
    std::string GetSelectedFile();
    int GetInstanceCount();
};

#endif
```

### 文件选择实现

```cpp
// 简化的文件选择对话框
bool SimpleGUI::OnBrowseFile() {
    OPENFILENAMEA ofn;
    char szFile[MAX_PATH] = {0};

    ZeroMemory(&ofn, sizeof(ofn));
    ofn.lStructSize = sizeof(ofn);
    ofn.hwndOwner = hWnd;
    ofn.lpstrFile = szFile;
    ofn.nMaxFile = sizeof(szFile);
    ofn.lpstrFilter = "可执行文件 (*.exe)\0*.exe\0所有文件 (*.*)\0*.*\0";
    ofn.lpstrTitle = "选择要多开的程序";
    ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST;

    if (GetOpenFileNameA(&ofn)) {
        SetWindowTextA(hFileEdit, szFile);
        return true;
    }

    return false;
}
```

## 通用检测引擎

### 自动检测算法

检测引擎负责自动识别目标软件使用的多开限制技术，无需用户配置。

#### 支持的限制类型
- **Mutex互斥体** - 最常见的单实例限制
- **文件锁定** - 独占访问配置文件或锁文件
- **注册表检查** - 通过注册表键值检测运行状态
- **窗口类检测** - 查找已存在的窗口实例
- **进程名检测** - 枚举进程列表查找同名进程
- **共享内存** - 使用内存映射文件进行通信

#### 自动检测引擎实现

```cpp
// AutoDetector.h - 自动检测引擎
#ifndef AUTODETECTOR_H
#define AUTODETECTOR_H

#include <windows.h>
#include <string>
#include <vector>

enum class RestrictionType {
    MUTEX_LOCK,
    FILE_LOCK,
    REGISTRY_CHECK,
    WINDOW_CHECK,
    PROCESS_CHECK,
    SHARED_MEMORY
};

struct DetectionResult {
    std::vector<RestrictionType> restrictions;
    double confidence;
    std::string recommendedStrategy;
};

class AutoDetector {
public:
    // 主要检测方法 - 自动分析目标程序
    DetectionResult AutoDetect(const std::string& exePath);

private:
    // PE文件分析
    bool AnalyzePEImports(const std::string& exePath, DetectionResult& result);

    // 字符串特征扫描
    bool AnalyzeStrings(const std::string& exePath, DetectionResult& result);

    // 运行时行为监控
    bool AnalyzeBehavior(const std::string& exePath, DetectionResult& result);

    // 策略选择
    std::string SelectBestStrategy(const std::vector<RestrictionType>& restrictions);
};

#endif
```

#### 检测算法实现

```cpp
// AutoDetector.cpp - 检测引擎实现
#include "AutoDetector.h"
#include <fstream>
#include <algorithm>

DetectionResult AutoDetector::AutoDetect(const std::string& exePath) {
    DetectionResult result;
    result.confidence = 0.0;

    // 1. PE导入表分析 - 检查导入的API函数
    AnalyzePEImports(exePath, result);

    // 2. 字符串特征扫描 - 查找特征字符串
    AnalyzeStrings(exePath, result);

    // 3. 运行时行为监控 - 短暂运行程序观察行为
    AnalyzeBehavior(exePath, result);

    // 4. 自动选择最佳绕过策略
    result.recommendedStrategy = SelectBestStrategy(result.restrictions);

    // 5. 计算总体置信度
    result.confidence = result.restrictions.empty() ? 0.1 : 0.8;

    return result;
}

bool AutoDetector::AnalyzePEImports(const std::string& exePath, DetectionResult& result) {
    // 简化的PE分析 - 检查导入的关键API
    HMODULE hModule = LoadLibraryExA(exePath.c_str(), NULL, DONT_RESOLVE_DLL_REFERENCES);
    if (!hModule) return false;

    // 检查是否导入了Mutex相关API
    if (GetProcAddress(hModule, "CreateMutexA") || GetProcAddress(hModule, "CreateMutexW")) {
        result.restrictions.push_back(RestrictionType::MUTEX_LOCK);
    }

    // 检查文件操作API
    if (GetProcAddress(hModule, "CreateFileA") || GetProcAddress(hModule, "CreateFileW")) {
        result.restrictions.push_back(RestrictionType::FILE_LOCK);
    }

    // 检查注册表API
    if (GetProcAddress(hModule, "RegOpenKeyA") || GetProcAddress(hModule, "RegOpenKeyW")) {
        result.restrictions.push_back(RestrictionType::REGISTRY_CHECK);
    }

    // 检查窗口查找API
    if (GetProcAddress(hModule, "FindWindowA") || GetProcAddress(hModule, "FindWindowW")) {
        result.restrictions.push_back(RestrictionType::WINDOW_CHECK);
    }

    FreeLibrary(hModule);
    return true;
}

bool AutoDetector::AnalyzeStrings(const std::string& exePath, DetectionResult& result) {
    // 简化的字符串扫描 - 查找特征字符串
    std::ifstream file(exePath, std::ios::binary);
    if (!file) return false;

    std::string content((std::istreambuf_iterator<char>(file)),
                       std::istreambuf_iterator<char>());

    // 查找Mutex特征字符串
    if (content.find("Mutex") != std::string::npos ||
        content.find("SingleInstance") != std::string::npos) {
        if (std::find(result.restrictions.begin(), result.restrictions.end(),
                     RestrictionType::MUTEX_LOCK) == result.restrictions.end()) {
            result.restrictions.push_back(RestrictionType::MUTEX_LOCK);
        }
    }

    // 查找文件锁特征
    if (content.find(".lock") != std::string::npos ||
        content.find("LOCK") != std::string::npos) {
        if (std::find(result.restrictions.begin(), result.restrictions.end(),
                     RestrictionType::FILE_LOCK) == result.restrictions.end()) {
            result.restrictions.push_back(RestrictionType::FILE_LOCK);
        }
    }

    return true;
}

std::string AutoDetector::SelectBestStrategy(const std::vector<RestrictionType>& restrictions) {
    if (restrictions.empty()) {
        return "PROCESS_ISOLATION";
    }

    // 根据检测到的限制类型选择最佳策略
    for (const auto& restriction : restrictions) {
        switch (restriction) {
            case RestrictionType::MUTEX_LOCK:
                return "MUTEX_HOOK";
            case RestrictionType::FILE_LOCK:
                return "FILE_REDIRECT";
            case RestrictionType::REGISTRY_CHECK:
                return "REGISTRY_VIRTUAL";
            case RestrictionType::WINDOW_CHECK:
                return "WINDOW_HOOK";
            case RestrictionType::PROCESS_CHECK:
                return "PROCESS_HIDE";
            default:
                break;
        }
    }

    return "COMBINED_STRATEGY";
}
```

## 自动绕过引擎

### 策略选择算法

绕过引擎根据检测结果自动选择和应用最佳的绕过策略，实现真正的一键多开。

#### 核心绕过策略
- **Mutex Hook** - Hook Mutex相关API，修改Mutex名称
- **文件重定向** - 将锁定文件重定向到独立目录
- **注册表虚拟化** - 为每个实例创建独立的注册表视图
- **窗口Hook** - Hook窗口查找API，隐藏已存在的窗口
- **进程隐藏** - 从进程枚举中隐藏已运行的实例
- **组合策略** - 同时应用多种绕过技术

#### 自动绕过引擎实现

```cpp
// AutoBypass.h - 自动绕过引擎
#ifndef AUTOBYPASS_H
#define AUTOBYPASS_H

#include <windows.h>
#include <string>
#include <vector>
#include "AutoDetector.h"

class AutoBypass {
public:
    // 主要方法 - 一键自动绕过
    bool AutoLaunch(const std::string& exePath, int instanceCount);

private:
    AutoDetector detector;

    // 策略应用方法
    bool ApplyMutexHook(const std::string& exePath);
    bool ApplyFileRedirect(const std::string& exePath);
    bool ApplyRegistryVirtual(const std::string& exePath);
    bool ApplyWindowHook(const std::string& exePath);
    bool ApplyProcessHide(const std::string& exePath);

    // 进程启动
    bool LaunchProcess(const std::string& exePath, int instanceId);

    // Hook DLL注入
    bool InjectHookDLL(DWORD processId, const std::string& dllName);

    // 清理资源
    void Cleanup();
};

#endif
```

#### 自动绕过实现

```cpp
// AutoBypass.cpp - 自动绕过引擎实现
#include "AutoBypass.h"
#include <iostream>

bool AutoBypass::AutoLaunch(const std::string& exePath, int instanceCount) {
    // 1. 自动检测限制类型
    DetectionResult detection = detector.AutoDetect(exePath);

    std::cout << "检测到限制类型数量: " << detection.restrictions.size() << std::endl;
    std::cout << "推荐策略: " << detection.recommendedStrategy << std::endl;

    // 2. 根据检测结果应用绕过策略
    bool success = true;

    for (const auto& restriction : detection.restrictions) {
        switch (restriction) {
            case RestrictionType::MUTEX_LOCK:
                success &= ApplyMutexHook(exePath);
                break;
            case RestrictionType::FILE_LOCK:
                success &= ApplyFileRedirect(exePath);
                break;
            case RestrictionType::REGISTRY_CHECK:
                success &= ApplyRegistryVirtual(exePath);
                break;
            case RestrictionType::WINDOW_CHECK:
                success &= ApplyWindowHook(exePath);
                break;
            case RestrictionType::PROCESS_CHECK:
                success &= ApplyProcessHide(exePath);
                break;
            default:
                break;
        }
    }

    if (!success) {
        std::cout << "绕过策略应用失败" << std::endl;
        return false;
    }

    // 3. 启动多个实例
    for (int i = 0; i < instanceCount; i++) {
        if (!LaunchProcess(exePath, i + 1)) {
            std::cout << "实例 " << (i + 1) << " 启动失败" << std::endl;
            return false;
        }

        std::cout << "实例 " << (i + 1) << " 启动成功" << std::endl;
        Sleep(1000); // 延迟1秒避免冲突
    }

    return true;
}

bool AutoBypass::ApplyMutexHook(const std::string& exePath) {
    // 应用Mutex Hook策略
    std::cout << "应用Mutex Hook策略..." << std::endl;

    // 这里会在目标进程启动后注入mutex_hook.dll
    return true;
}

bool AutoBypass::ApplyFileRedirect(const std::string& exePath) {
    // 应用文件重定向策略
    std::cout << "应用文件重定向策略..." << std::endl;

    // 创建独立的文件目录
    std::string tempDir = "C:\\Temp\\MultiLauncher\\" + std::to_string(GetCurrentProcessId());
    CreateDirectoryA(tempDir.c_str(), NULL);

    return true;
}

bool AutoBypass::LaunchProcess(const std::string& exePath, int instanceId) {
    STARTUPINFOA si = {0};
    PROCESS_INFORMATION pi = {0};
    si.cb = sizeof(si);

    // 启动进程
    if (!CreateProcessA(exePath.c_str(), NULL, NULL, NULL, FALSE,
                       CREATE_SUSPENDED, NULL, NULL, &si, &pi)) {
        return false;
    }

    // 注入Hook DLL
    InjectHookDLL(pi.dwProcessId, "mutex_hook.dll");

    // 恢复进程执行
    ResumeThread(pi.hThread);

    CloseHandle(pi.hThread);
    CloseHandle(pi.hProcess);

    return true;
}

bool AutoBypass::InjectHookDLL(DWORD processId, const std::string& dllName) {
    // 简化的DLL注入实现
    HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
    if (!hProcess) return false;

    // 这里实现DLL注入逻辑
    // 使用CreateRemoteThread + LoadLibrary方法

    CloseHandle(hProcess);
    return true;
}
```

## 反检测技术

### 隐蔽技术模块

反检测模块确保多开过程对目标软件完全透明，防止被检测到。

#### 核心隐蔽技术
- **DLL隐藏** - 从PEB模块列表中隐藏注入的DLL
- **Hook隐蔽** - 隐藏API Hook痕迹，防止被扫描检测
- **反调试绕过** - 绕过常见的反调试检测技术
- **进程隐藏** - 从进程枚举中隐藏多开实例

#### 反检测技术实现

```cpp
// StealthModule.h - 反检测模块
#ifndef STEALTHMODULE_H
#define STEALTHMODULE_H

#include <windows.h>
#include <string>

class StealthModule {
public:
    // 启用所有隐蔽技术
    bool EnableStealth();

    // DLL隐藏
    bool HideDLLFromPEB(HMODULE hModule);

    // Hook隐蔽
    bool HideHookTraces();

    // 反调试绕过
    bool BypassAntiDebug();

    // 进程隐藏
    bool HideFromProcessList();

private:
    // 修改PEB结构
    bool ModifyPEB();

    // 清理Hook痕迹
    bool CleanHookSignatures();

    // 伪造调试器检测结果
    bool FakeDebuggerChecks();
};

#endif
```

## 核心Hook DLL实现

### Mutex Hook DLL

```cpp
// mutex_hook.cpp - Mutex绕过Hook DLL
#include <windows.h>
#include <detours.h>
#include <string>

// 原始API函数指针
static HANDLE (WINAPI* TrueCreateMutexA)(LPSECURITY_ATTRIBUTES, BOOL, LPCSTR) = CreateMutexA;
static HANDLE (WINAPI* TrueCreateMutexW)(LPSECURITY_ATTRIBUTES, BOOL, LPCWSTR) = CreateMutexW;
static HANDLE (WINAPI* TrueOpenMutexA)(DWORD, BOOL, LPCSTR) = OpenMutexA;
static HANDLE (WINAPI* TrueOpenMutexW)(DWORD, BOOL, LPCWSTR) = OpenMutexW;

// Hook函数实现
HANDLE WINAPI HookedCreateMutexA(LPSECURITY_ATTRIBUTES lpMutexAttributes,
                                BOOL bInitialOwner, LPCSTR lpName) {
    if (lpName) {
        // 为Mutex名称添加进程ID后缀，实现实例隔离
        std::string newName = std::string(lpName) + "_" + std::to_string(GetCurrentProcessId());
        return TrueCreateMutexA(lpMutexAttributes, bInitialOwner, newName.c_str());
    }
    return TrueCreateMutexA(lpMutexAttributes, bInitialOwner, lpName);
}

HANDLE WINAPI HookedOpenMutexA(DWORD dwDesiredAccess, BOOL bInheritHandle, LPCSTR lpName) {
    if (lpName) {
        std::string newName = std::string(lpName) + "_" + std::to_string(GetCurrentProcessId());
        return TrueOpenMutexA(dwDesiredAccess, bInheritHandle, newName.c_str());
    }
    return TrueOpenMutexA(dwDesiredAccess, bInheritHandle, lpName);
}

// DLL入口点
BOOL APIENTRY DllMain(HMODULE hModule, DWORD dwReason, LPVOID lpReserved) {
    switch (dwReason) {
        case DLL_PROCESS_ATTACH:
            // 安装Hook
            DetourTransactionBegin();
            DetourUpdateThread(GetCurrentThread());
            DetourAttach(&(PVOID&)TrueCreateMutexA, HookedCreateMutexA);
            DetourAttach(&(PVOID&)TrueOpenMutexA, HookedOpenMutexA);
            DetourTransactionCommit();
            break;

        case DLL_PROCESS_DETACH:
            // 卸载Hook
            DetourTransactionBegin();
            DetourUpdateThread(GetCurrentThread());
            DetourDetach(&(PVOID&)TrueCreateMutexA, HookedCreateMutexA);
            DetourDetach(&(PVOID&)TrueOpenMutexA, HookedOpenMutexA);
            DetourTransactionCommit();
            break;
    }
    return TRUE;
}
```

## 编译和使用

### 编译步骤

```bash
# 1. 克隆项目
git clone https://github.com/username/multi-launcher.git
cd multi-launcher

# 2. 编译项目
mkdir build && cd build
cmake .. -G "Visual Studio 16 2019" -A x64
cmake --build . --config Release

# 3. 运行程序
cd Release
MultiLauncher.exe
```

### 使用方法

#### 基本使用
1. **启动程序** - 双击MultiLauncher.exe
2. **选择程序** - 点击"浏览"按钮选择要多开的程序
3. **设置数量** - 调整实例数量（默认为3）
4. **启动多开** - 点击"启动多开"按钮

#### 自动化流程
程序会自动执行以下步骤：
1. 分析目标程序的限制类型
2. 选择最佳的绕过策略
3. 注入相应的Hook DLL
4. 启动多个程序实例

## 常见问题

### 程序无法启动
**问题**: 双击程序没有反应或提示错误
**解决方案**:
1. 确保以管理员身份运行
2. 检查是否安装了Visual C++ Redistributable
3. 确认Windows版本兼容性（支持Windows 7-11）

### 多开失败
**问题**: 点击启动多开后只启动了一个实例
**解决方案**:
1. 检查目标程序是否使用了未知的限制技术
2. 尝试以管理员身份运行万能多开器
3. 确认目标程序路径正确且文件存在

### 被杀毒软件误报
**问题**: 杀毒软件提示程序有风险
**解决方案**:
1. 将程序添加到杀毒软件白名单
2. 这是正常现象，因为程序使用了Hook技术
3. 可以查看程序的数字签名验证安全性

### 反调试检测的绕过策略

#### 反反调试实现
```cpp
// AntiAntiDebug.h - 反反调试技术
#ifndef ANTIANTIDEBUG_H
#define ANTIANTIDEBUG_H

#include <windows.h>
#include <winternl.h>
#include <vector>
#include <map>
#include <functional>

// 反调试检测类型
enum class AntiDebugType {
    ISDEBUGGER_PRESENT,     // IsDebuggerPresent检测
    PEB_CHECK,              // PEB检查
    HEAP_FLAGS,             // 堆标志检查
    NTGlobalFlag,           // NTGlobalFlag检查
    QUERY_INFORMATION,      // 查询信息检测
    DEBUG_PORT,             // 调试端口检测
    TIMING_CHECK,           // 时间检测
    EXCEPTION_CHECK,        // 异常检测
    HARDWARE_BREAKPOINT,    // 硬件断点检测
    SOFTWARE_BREAKPOINT,    // 软件断点检测
    MEMORY_PROTECTION,      // 内存保护检测
    THREAD_CONTEXT,         // 线程上下文检测
    PROCESS_ENVIRONMENT     // 进程环境检测
};

// 绕过策略
struct BypassStrategy {
    AntiDebugType type;
    std::function<bool()> bypassFunction;
    std::string description;
    bool isActive;
};

class AntiAntiDebug {
private:
    std::map<AntiDebugType, BypassStrategy> strategies; // 绕过策略
    std::vector<PVOID> hookedFunctions;                // 已Hook的函数
    bool isProtectionActive;                           // 保护是否激活

public:
    AntiAntiDebug();
    ~AntiAntiDebug();

    // 初始化反反调试保护
    bool InitializeProtection();

    // 绕过IsDebuggerPresent检测
    bool BypassIsDebuggerPresent();

    // 绕过PEB检查
    bool BypassPEBCheck();

    // 绕过堆标志检查
    bool BypassHeapFlags();

    // 绕过NTGlobalFlag检查
    bool BypassNTGlobalFlag();

    // 绕过查询信息检测
    bool BypassQueryInformation();

    // 绕过调试端口检测
    bool BypassDebugPort();

    // 绕过时间检测
    bool BypassTimingCheck();

    // 绕过异常检测
    bool BypassExceptionCheck();

    // 绕过硬件断点检测
    bool BypassHardwareBreakpoint();

    // 绕过软件断点检测
    bool BypassSoftwareBreakpoint();

    // 启用所有绕过策略
    bool EnableAllBypasses();

    // 禁用所有绕过策略
    bool DisableAllBypasses();

    // 检测反调试技术
    std::vector<AntiDebugType> DetectAntiDebugTechniques(const std::string& exePath);

private:
    // Hook API函数
    bool HookAPIFunction(const std::string& moduleName, const std::string& functionName,
                        PVOID hookFunction, PVOID& originalFunction);

    // 修改PEB结构
    bool ModifyPEB();

    // 修改堆结构
    bool ModifyHeapFlags();

    // 伪造时间信息
    bool FakeTimingInfo();

    // 处理异常
    LONG WINAPI ExceptionHandler(PEXCEPTION_POINTERS pExceptionInfo);

    // 清理调试痕迹
    bool CleanDebugTraces();
};

// Hook函数声明
BOOL WINAPI HookedIsDebuggerPresent();
NTSTATUS WINAPI HookedNtQueryInformationProcess(HANDLE ProcessHandle,
    PROCESSINFOCLASS ProcessInformationClass, PVOID ProcessInformation,
    ULONG ProcessInformationLength, PULONG ReturnLength);
DWORD WINAPI HookedGetTickCount();
BOOL WINAPI HookedQueryPerformanceCounter(LARGE_INTEGER* lpPerformanceCount);

#endif
```

### 虚拟环境检测应对方案

#### VMware和VirtualBox检测绕过
```cpp
// VMEvasion.h - 虚拟机逃逸技术
#ifndef VMEVASION_H
#define VMEVASION_H

#include <windows.h>
#include <string>
#include <vector>
#include <map>

// 虚拟机类型
enum class VMType {
    VMWARE,
    VIRTUALBOX,
    HYPER_V,
    QEMU,
    XEN,
    PARALLELS,
    UNKNOWN
};

// 检测方法
enum class DetectionMethod {
    REGISTRY_CHECK,         // 注册表检查
    FILE_CHECK,             // 文件检查
    PROCESS_CHECK,          // 进程检查
    SERVICE_CHECK,          // 服务检查
    HARDWARE_CHECK,         // 硬件检查
    TIMING_CHECK,           // 时间检查
    CPUID_CHECK,            // CPUID检查
    MAC_ADDRESS_CHECK,      // MAC地址检查
    MEMORY_CHECK,           // 内存检查
    DEVICE_CHECK            // 设备检查
};

// 虚拟机特征
struct VMSignature {
    VMType type;
    DetectionMethod method;
    std::string signature;
    std::string description;
};

class VMEvasion {
private:
    std::vector<VMSignature> vmSignatures;      // 虚拟机特征库
    std::map<VMType, bool> detectedVMs;         // 检测到的虚拟机
    bool isEvasionActive;                       // 逃逸是否激活

public:
    VMEvasion();
    ~VMEvasion();

    // 初始化虚拟机逃逸
    bool InitializeEvasion();

    // 检测虚拟机环境
    std::vector<VMType> DetectVirtualMachines();

    // 伪装物理机环境
    bool DisguiseAsPhysicalMachine();

    // 绕过VMware检测
    bool BypassVMwareDetection();

    // 绕过VirtualBox检测
    bool BypassVirtualBoxDetection();

    // 绕过Hyper-V检测
    bool BypassHyperVDetection();

    // 修改注册表特征
    bool ModifyRegistrySignatures();

    // 隐藏虚拟机文件
    bool HideVMFiles();

    // 伪造硬件信息
    bool FakeHardwareInfo();

    // 修改MAC地址
    bool ModifyMACAddress();

    // 伪造CPUID信息
    bool FakeCPUIDInfo();

    // 调整时间特征
    bool AdjustTimingCharacteristics();

private:
    // 加载虚拟机特征库
    void LoadVMSignatures();

    // 检查注册表特征
    bool CheckRegistrySignature(const VMSignature& signature);

    // 检查文件特征
    bool CheckFileSignature(const VMSignature& signature);

    // 检查进程特征
    bool CheckProcessSignature(const VMSignature& signature);

    // 检查服务特征
    bool CheckServiceSignature(const VMSignature& signature);

    // 检查硬件特征
    bool CheckHardwareSignature(const VMSignature& signature);

    // Hook相关API
    bool HookVMDetectionAPIs();

    // 创建假的硬件设备
    bool CreateFakeHardwareDevices();
};

#endif
```

### 进程注入痕迹清除方法

#### 进程注入技术增强
```cpp
class ProcessInjector {
public:
    // 方法1: CreateRemoteThread + LoadLibrary（增强版）
    bool InjectDLL_CreateRemoteThread(DWORD processId, const string& dllPath);

    // 方法2: Manual DLL Mapping（隐蔽版）
    bool InjectDLL_ManualMapping(DWORD processId, const string& dllPath);

    // 方法3: SetWindowsHookEx（全局Hook）
    bool InjectDLL_SetHook(DWORD processId, const string& dllPath);

    // 方法4: NtCreateThreadEx（原生API）
    bool InjectDLL_NtCreateThread(DWORD processId, const string& dllPath);

    // 方法5: Process Hollowing（进程挖空）
    bool InjectDLL_ProcessHollowing(const string& targetProcess, const string& dllPath);

    // 方法6: Atom Bombing（原子轰炸）
    bool InjectDLL_AtomBombing(DWORD processId, const string& dllPath);

    // 方法7: Thread Execution Hijacking（线程劫持）
    bool InjectDLL_ThreadHijacking(DWORD processId, const string& dllPath);

    // 清除注入痕迹
    bool CleanInjectionTraces(DWORD processId, HMODULE hModule);

private:
    // 清除远程线程痕迹
    bool CleanRemoteThreadTraces(HANDLE hThread);

    // 清除内存分配痕迹
    bool CleanMemoryAllocationTraces(HANDLE hProcess, PVOID address);

    // 清除模块加载痕迹
    bool CleanModuleLoadTraces(HANDLE hProcess, HMODULE hModule);
};
```

## API参考

### 主要类接口

#### UniversalMultiLauncher
```cpp
class UniversalMultiLauncher {
public:
    bool Initialize();
    DetectionResult AnalyzeApplication(const string& exePath);
    bool LaunchWithBypass(const string& exePath, int instanceCount = 1);
    bool SaveAnalysisResult(const string& exePath, const DetectionResult& result);
    bool LoadKnownApps();
    
private:
    map<string, DetectionResult> appDatabase;
    vector<BypassStrategy> strategies;
};
```

#### RestrictionDetector
```cpp
class RestrictionDetector {
public:
    DetectionResult AnalyzeApp(const string& exePath);
    bool AnalyzePEImports(const string& exePath, DetectionResult& result);
    bool MonitorRuntimeBehavior(const string& exePath, DetectionResult& result);
    bool ScanMemoryPatterns(DetectionResult& result);
    
private:
    bool FindAPIInImports(const string& exePath, const string& apiName);
    void ClassifyRestrictionType(const string& apiName, DetectionResult& result);
};
```

#### BypassEngine
```cpp
class BypassEngine {
public:
    bool ApplyBypass(const string& exePath, const DetectionResult& detection);
    bool BypassMutex(const string& exePath, const DetectionResult& detection);
    bool BypassFileLock(const string& exePath, const DetectionResult& detection);
    bool BypassRegistryLock(const string& exePath, const DetectionResult& detection);
    
private:
    bool InjectHookDLL(const string& exePath, const string& dllPath, const string& hookName);
    string CreateInstanceDirectory(const string& exePath);
    string CreateVirtualRegistryBranch();
};
```

## 编译指南

### Visual Studio编译
```batch
# 1. 打开Developer Command Prompt
# 2. 编译主程序
cl /EHsc /I"include" /I"libs/detours/include" src/main.cpp src/core/*.cpp src/utils/*.cpp /link libs/detours/lib.X64/detours.lib psapi.lib advapi32.lib kernel32.lib user32.lib /OUT:MultiLauncher.exe

# 3. 编译Hook DLL
cl /LD /EHsc /I"libs/detours/include" src/hooks/mutex_hook.cpp /link libs/detours/lib.X64/detours.lib /OUT:mutex_hook.dll
cl /LD /EHsc /I"libs/detours/include" src/hooks/file_hook.cpp /link libs/detours/lib.X64/detours.lib /OUT:file_hook.dll
cl /LD /EHsc /I"libs/detours/include" src/hooks/registry_hook.cpp /link libs/detours/lib.X64/detours.lib /OUT:registry_hook.dll
cl /LD /EHsc /I"libs/detours/include" src/hooks/window_hook.cpp /link libs/detours/lib.X64/detours.lib /OUT:window_hook.dll
```

### CMake编译
```batch
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

### MinGW编译
```batch
g++ -std=c++17 -O2 -I"include" -I"libs/detours/include" src/main.cpp src/core/*.cpp src/utils/*.cpp -L"libs/detours/lib" -ldetours -lpsapi -ladvapi32 -lkernel32 -luser32 -lshlwapi -o MultiLauncher.exe

g++ -shared -std=c++17 -O2 -I"libs/detours/include" src/hooks/mutex_hook.cpp -L"libs/detours/lib" -ldetours -o mutex_hook.dll
```

## 测试指南

### 单元测试
```cpp
// tests/test_detector.cpp
#include "gtest/gtest.h"
#include "RestrictionDetector.h"

TEST(RestrictionDetectorTest, DetectMutex) {
    RestrictionDetector detector;
    DetectionResult result = detector.AnalyzeApp("test_apps/mutex_app.exe");
    
    EXPECT_TRUE(find(result.restrictions.begin(), result.restrictions.end(), 
                    RestrictionType::MUTEX_LOCK) != result.restrictions.end());
}

TEST(RestrictionDetectorTest, DetectFileLock) {
    RestrictionDetector detector;
    DetectionResult result = detector.AnalyzeApp("test_apps/file_lock_app.exe");
    
    EXPECT_TRUE(find(result.restrictions.begin(), result.restrictions.end(), 
                    RestrictionType::FILE_LOCK) != result.restrictions.end());
}
```

### 集成测试
```cpp
// tests/test_integration.cpp
TEST(IntegrationTest, LaunchMultipleInstances) {
    UniversalMultiLauncher launcher;
    ASSERT_TRUE(launcher.Initialize());
    
    string testApp = "test_apps/sample_app.exe";
    EXPECT_TRUE(launcher.LaunchWithBypass(testApp, 3));
    
    // 验证确实启动了3个实例
    int instanceCount = CountProcessInstances("sample_app.exe");
    EXPECT_EQ(instanceCount, 3);
}
```

### 测试应用程序
创建简单的测试程序来验证各种限制机制：

```cpp
// test_apps/mutex_test.cpp - Mutex限制测试
int main() {
    HANDLE hMutex = CreateMutexA(NULL, TRUE, "TestAppMutex");
    if (GetLastError() == ERROR_ALREADY_EXISTS) {
        MessageBoxA(NULL, "应用程序已在运行", "错误", MB_OK);
        return 1;
    }
    
    MessageBoxA(NULL, "应用程序启动成功", "信息", MB_OK);
    CloseHandle(hMutex);
    return 0;
}
```

## 用户交互流程

### 完整操作步骤

#### 1. 程序启动和初始化
```
用户启动程序 → 加载配置文件 → 初始化检测引擎 → 显示主界面
```

**详细流程：**
1. **程序启动**：双击MultiLauncher.exe或通过命令行启动
2. **权限检查**：自动检测并请求管理员权限（如需要）
3. **环境检测**：检测操作系统版本、.NET Framework版本等
4. **配置加载**：加载用户设置、规则库、特征数据库
5. **界面初始化**：创建主窗口、设置拖拽支持、加载主题

#### 2. 软件选择阶段
```
方式A: 文件选择对话框
用户点击"浏览"按钮 → 打开文件选择对话框 → 选择目标程序 → 自动填充路径

方式B: 拖拽操作
用户拖拽文件到程序窗口 → 验证文件类型 → 自动填充路径 → 显示确认提示
```

**界面反馈：**
- 文件路径实时显示在编辑框中
- 状态栏显示"已选择文件：xxx.exe"
- 自动启用"分析"和"启动"按钮
- 显示文件基本信息（大小、版本、数字签名状态）

#### 3. 程序分析阶段
```
用户点击"分析"按钮 → 显示进度对话框 → 执行多阶段检测 → 显示检测结果
```

**分析进度显示：**
```
┌─分析进度─────────────────────────────────────┐
│ 当前阶段: 静态分析 - PE文件结构解析          │
│ 进度: ████████████████████████████ 75%      │
│                                             │
│ 已完成:                                     │
│ ✓ 文件完整性检查                            │
│ ✓ 数字签名验证                              │
│ ✓ PE导入表分析                              │
│ ✓ 字符串特征提取                            │
│                                             │
│ 正在执行:                                   │
│ ⟳ 动态行为分析                              │
│                                             │
│ 待执行:                                     │
│ ○ 机器学习分类                              │
│ ○ 规则引擎评估                              │
│ ○ 置信度计算                                │
│                                             │
│ [取消分析]                                  │
└─────────────────────────────────────────────┘
```

#### 4. 结果展示阶段
```
分析完成 → 更新检测结果列表 → 显示置信度 → 提供绕过建议
```

**检测结果界面：**
```
┌─检测结果─────────────────────────────────────┐
│ 检测项目          状态    置信度    证据数量  │
│ ─────────────────────────────────────────── │
│ Mutex互斥体       ✓检测到   95%      3      │
│ 文件锁定          ✓检测到   87%      2      │
│ 注册表检查        ✗未检测   12%      0      │
│ 窗口类检测        ✓检测到   78%      1      │
│ 进程名检测        ✗未检测   23%      0      │
│ 共享内存          ✓检测到   91%      2      │
│ ─────────────────────────────────────────── │
│ 总体评估: 高限制程度 (置信度: 84%)           │
│ 推荐策略: Mutex Hook + 文件重定向            │
│                                             │
│ [查看详细报告] [导出结果] [开始绕过]         │
└─────────────────────────────────────────────┘
```

#### 5. 多开启动阶段
```
用户设置实例数量 → 点击"启动多开" → 执行绕过策略 → 启动多个实例
```

**启动进度显示：**
```
┌─多开启动─────────────────────────────────────┐
│ 目标程序: TestApp.exe                       │
│ 实例数量: 3                                 │
│ 当前进度: 正在启动第2个实例...               │
│                                             │
│ 实例状态:                                   │
│ 实例1: ✓已启动 (PID: 1234) - 运行正常       │
│ 实例2: ⟳启动中... 应用绕过策略              │
│ 实例3: ○等待启动                            │
│                                             │
│ 绕过策略:                                   │
│ ✓ Mutex Hook已注入                          │
│ ✓ 文件重定向已设置                          │
│ ✓ 注册表虚拟化已启用                        │
│                                             │
│ [停止启动] [查看日志]                       │
└─────────────────────────────────────────────┘
```

### 错误处理和用户提示

#### 常见错误场景处理
```cpp
// 错误处理示例
class ErrorHandler {
public:
    // 文件选择错误
    static void HandleFileSelectionError(const std::string& error) {
        std::string message = "文件选择失败：\n" + error +
                             "\n\n请确保：\n"
                             "1. 文件确实存在\n"
                             "2. 文件是有效的可执行文件\n"
                             "3. 您有足够的访问权限";
        MessageBoxA(NULL, message.c_str(), "文件选择错误", MB_OK | MB_ICONERROR);
    }

    // 权限不足错误
    static void HandlePermissionError() {
        std::string message = "权限不足，无法执行操作。\n\n"
                             "请尝试：\n"
                             "1. 以管理员身份运行程序\n"
                             "2. 检查目标程序是否被其他安全软件保护\n"
                             "3. 暂时关闭杀毒软件的实时保护";
        MessageBoxA(NULL, message.c_str(), "权限错误", MB_OK | MB_ICONWARNING);
    }

    // 分析失败错误
    static void HandleAnalysisError(const std::string& details) {
        std::string message = "程序分析失败：\n" + details +
                             "\n\n可能的原因：\n"
                             "1. 目标程序使用了未知的保护技术\n"
                             "2. 程序文件已损坏或被加密\n"
                             "3. 系统资源不足\n\n"
                             "建议：尝试使用手动模式或联系技术支持";
        MessageBoxA(NULL, message.c_str(), "分析错误", MB_OK | MB_ICONERROR);
    }

    // 启动失败错误
    static void HandleLaunchError(int instanceNumber, const std::string& error) {
        char message[512];
        sprintf_s(message, "第%d个实例启动失败：\n%s\n\n"
                          "可能的解决方案：\n"
                          "1. 检查目标程序是否需要特定的运行环境\n"
                          "2. 确认系统资源充足\n"
                          "3. 尝试降低同时启动的实例数量",
                  instanceNumber, error.c_str());
        MessageBoxA(NULL, message, "启动错误", MB_OK | MB_ICONERROR);
    }
};
```

### 高级功能操作指南

#### 批量处理模式
```
1. 点击菜单"工具" → "批量处理"
2. 添加多个目标程序到列表
3. 为每个程序设置实例数量
4. 点击"开始批量处理"
5. 监控整体进度和各程序状态
```

#### 配置文件管理
```
1. 点击菜单"文件" → "配置管理"
2. 选择"导入配置"或"导出配置"
3. 选择配置文件路径
4. 确认导入/导出操作
```

#### 日志查看和导出
```
1. 在主界面点击"查看日志"按钮
2. 选择日志级别过滤（信息/警告/错误）
3. 使用搜索功能查找特定内容
4. 点击"导出日志"保存到文件
```

## 性能基准测试

### 性能指标定义

#### 内存使用指标
- **基础内存占用**：程序启动后的初始内存使用量
- **峰值内存占用**：分析过程中的最大内存使用量
- **内存增长率**：随实例数量增加的内存增长情况
- **内存泄漏检测**：长时间运行后的内存变化

#### CPU使用指标
- **空闲状态CPU占用**：程序待机时的CPU使用率
- **分析阶段CPU占用**：执行检测分析时的CPU使用率
- **启动阶段CPU占用**：多开启动时的CPU使用率
- **CPU使用峰值**：各阶段的最大CPU使用率

#### 响应时间指标
- **界面响应时间**：用户操作到界面反馈的时间
- **文件分析时间**：完成程序分析所需的时间
- **实例启动时间**：单个实例启动所需的时间
- **总体处理时间**：从开始到完成的总时间

### 基准测试实现

```cpp
// PerformanceBenchmark.h - 性能基准测试
#ifndef PERFORMANCEBENCHMARK_H
#define PERFORMANCEBENCHMARK_H

#include <windows.h>
#include <psapi.h>
#include <string>
#include <vector>
#include <chrono>
#include <map>

// 性能指标结构
struct PerformanceMetrics {
    // 内存指标
    SIZE_T baseMemoryUsage;         // 基础内存使用(KB)
    SIZE_T peakMemoryUsage;         // 峰值内存使用(KB)
    SIZE_T currentMemoryUsage;      // 当前内存使用(KB)
    double memoryGrowthRate;        // 内存增长率(%)

    // CPU指标
    double idleCPUUsage;            // 空闲CPU使用率(%)
    double analysisCPUUsage;        // 分析CPU使用率(%)
    double launchCPUUsage;          // 启动CPU使用率(%)
    double peakCPUUsage;            // 峰值CPU使用率(%)

    // 时间指标
    DWORD uiResponseTime;           // 界面响应时间(ms)
    DWORD analysisTime;             // 分析时间(ms)
    DWORD launchTime;               // 启动时间(ms)
    DWORD totalProcessTime;         // 总处理时间(ms)

    // 其他指标
    int successfulLaunches;         // 成功启动数量
    int failedLaunches;             // 失败启动数量
    double successRate;             // 成功率(%)
};

// 测试场景
enum class TestScenario {
    LIGHT_LOAD,         // 轻负载（1-2个实例）
    MEDIUM_LOAD,        // 中负载（3-5个实例）
    HEAVY_LOAD,         // 重负载（6-10个实例）
    STRESS_TEST,        // 压力测试（10+个实例）
    MEMORY_STRESS,      // 内存压力测试
    CPU_STRESS          // CPU压力测试
};

class PerformanceBenchmark {
private:
    PerformanceMetrics currentMetrics;
    std::map<TestScenario, PerformanceMetrics> benchmarkResults;
    std::chrono::high_resolution_clock::time_point startTime;
    HANDLE hProcess;
    bool isMonitoring;

public:
    PerformanceBenchmark();
    ~PerformanceBenchmark();

    // 开始性能监控
    bool StartMonitoring();

    // 停止性能监控
    bool StopMonitoring();

    // 执行基准测试
    bool RunBenchmark(TestScenario scenario, const std::string& testApp);

    // 获取当前性能指标
    PerformanceMetrics GetCurrentMetrics();

    // 记录时间点
    void MarkTimePoint(const std::string& eventName);

    // 更新内存使用情况
    void UpdateMemoryUsage();

    // 更新CPU使用情况
    void UpdateCPUUsage();

    // 生成性能报告
    std::string GeneratePerformanceReport();

    // 导出基准测试结果
    bool ExportBenchmarkResults(const std::string& filePath);

    // 比较性能指标
    std::string CompareMetrics(const PerformanceMetrics& baseline,
                              const PerformanceMetrics& current);

private:
    // 获取进程内存信息
    bool GetProcessMemoryInfo(PROCESS_MEMORY_COUNTERS& memInfo);

    // 获取系统CPU使用率
    double GetSystemCPUUsage();

    // 获取进程CPU使用率
    double GetProcessCPUUsage();

    // 计算时间差
    DWORD CalculateTimeDifference(const std::chrono::high_resolution_clock::time_point& start);

    // 内存泄漏检测
    bool DetectMemoryLeaks();

    // 性能瓶颈分析
    std::vector<std::string> AnalyzeBottlenecks();
};

#endif
```

### 基准测试标准

#### 内存使用标准
```
优秀: 基础内存 < 50MB, 峰值内存 < 200MB
良好: 基础内存 < 100MB, 峰值内存 < 500MB
一般: 基础内存 < 200MB, 峰值内存 < 1GB
需优化: 基础内存 > 200MB 或 峰值内存 > 1GB
```

#### CPU使用标准
```
优秀: 空闲 < 1%, 分析 < 30%, 启动 < 50%
良好: 空闲 < 3%, 分析 < 50%, 启动 < 70%
一般: 空闲 < 5%, 分析 < 70%, 启动 < 90%
需优化: 空闲 > 5% 或 分析 > 70% 或 启动 > 90%
```

#### 响应时间标准
```
优秀: 界面响应 < 100ms, 分析 < 5s, 启动 < 3s
良好: 界面响应 < 200ms, 分析 < 10s, 启动 < 5s
一般: 界面响应 < 500ms, 分析 < 20s, 启动 < 10s
需优化: 界面响应 > 500ms 或 分析 > 20s 或 启动 > 10s
```

## 使用说明

### 命令行用法
```batch
# 基本用法
MultiLauncher.exe "C:\Program Files\TestApp\app.exe"

# 启动多个实例
MultiLauncher.exe "C:\Program Files\TestApp\app.exe" 3

# 分析模式（仅分析不启动）
MultiLauncher.exe --analyze "C:\Program Files\TestApp\app.exe"

# 详细输出
MultiLauncher.exe --verbose "C:\Program Files\TestApp\app.exe"

# 保存分析结果
MultiLauncher.exe --save-profile "C:\Program Files\TestApp\app.exe"

# 批量处理模式
MultiLauncher.exe --batch "config\batch_config.json"

# 性能基准测试
MultiLauncher.exe --benchmark --scenario=stress "C:\Program Files\TestApp\app.exe"

# 静默模式（无GUI）
MultiLauncher.exe --silent --instances=5 "C:\Program Files\TestApp\app.exe"
```

## 常见商业软件绕过方法

### Office系列软件

#### Microsoft Word
```json
{
  "name": "Microsoft Word",
  "executable": "WINWORD.EXE",
  "version_range": "2016-2021",
  "restrictions": ["MUTEX_LOCK", "FILE_LOCK", "REGISTRY_CHECK"],
  "known_mutexes": [
    "_MSO_DW_{PID}",
    "OfficeClickToRunSvc",
    "WinWordStartupMutex"
  ],
  "bypass_strategies": [
    {
      "type": "MUTEX_LOCK",
      "method": "API_HOOK",
      "dll": "office_mutex_hook.dll",
      "success_rate": 95,
      "notes": "Hook CreateMutex系列API，动态修改Mutex名称"
    },
    {
      "type": "FILE_LOCK",
      "method": "FILE_REDIRECT",
      "target_dir": "%APPDATA%\\Microsoft\\Word\\MultiInstance\\{PID}",
      "files_to_redirect": [
        "Normal.dotm",
        "Recent.dat",
        "Word.qat"
      ],
      "success_rate": 90
    },
    {
      "type": "REGISTRY_CHECK",
      "method": "REGISTRY_VIRTUALIZATION",
      "base_key": "HKCU\\Software\\Microsoft\\Office\\16.0\\Word",
      "success_rate": 85
    }
  ],
  "special_notes": "需要处理COM组件注册和DDE通信"
}
```

#### Microsoft Excel
```json
{
  "name": "Microsoft Excel",
  "executable": "EXCEL.EXE",
  "version_range": "2016-2021",
  "restrictions": ["MUTEX_LOCK", "FILE_LOCK", "COM_REGISTRATION"],
  "known_mutexes": [
    "_MSO_DW_{PID}",
    "ExcelStartupMutex",
    "ExcelDDEMutex"
  ],
  "bypass_strategies": [
    {
      "type": "MUTEX_LOCK",
      "method": "API_HOOK",
      "dll": "office_mutex_hook.dll",
      "success_rate": 93
    },
    {
      "type": "COM_REGISTRATION",
      "method": "COM_ISOLATION",
      "isolated_classes": [
        "Excel.Application",
        "Excel.Sheet",
        "Excel.Chart"
      ],
      "success_rate": 88
    }
  ],
  "launch_parameters": "/automation -Embedding",
  "special_notes": "Excel的COM自动化接口需要特殊处理"
}
```

### Adobe系列软件

#### Adobe Photoshop
```json
{
  "name": "Adobe Photoshop",
  "executable": "Photoshop.exe",
  "version_range": "CC 2019-2023",
  "restrictions": ["MUTEX_LOCK", "SHARED_MEMORY", "HARDWARE_ID", "LICENSE_CHECK"],
  "known_mutexes": [
    "PhotoshopSingleInstance",
    "AdobeCreativeCloudMutex",
    "PSAutoRecover"
  ],
  "bypass_strategies": [
    {
      "type": "MUTEX_LOCK",
      "method": "API_HOOK",
      "dll": "adobe_mutex_hook.dll",
      "success_rate": 85,
      "notes": "Adobe使用多层Mutex保护"
    },
    {
      "type": "SHARED_MEMORY",
      "method": "MEMORY_ISOLATION",
      "shared_sections": [
        "AdobeIPCMemory",
        "PhotoshopSharedData"
      ],
      "success_rate": 80
    },
    {
      "type": "HARDWARE_ID",
      "method": "HARDWARE_SPOOFING",
      "spoof_targets": ["MAC_ADDRESS", "CPU_ID", "MOTHERBOARD_ID"],
      "success_rate": 75
    },
    {
      "type": "LICENSE_CHECK",
      "method": "LICENSE_BYPASS",
      "bypass_dll": "adobe_license_hook.dll",
      "success_rate": 70,
      "legal_warning": "仅用于测试目的，请确保拥有合法许可证"
    }
  ],
  "special_requirements": [
    "需要管理员权限",
    "可能触发Adobe Creative Cloud检测",
    "建议在虚拟机中测试"
  ]
}
```

#### Adobe Acrobat Reader
```json
{
  "name": "Adobe Acrobat Reader",
  "executable": "AcroRd32.exe",
  "version_range": "DC 2020-2023",
  "restrictions": ["MUTEX_LOCK", "FILE_LOCK", "PROCESS_CHECK"],
  "bypass_strategies": [
    {
      "type": "MUTEX_LOCK",
      "method": "API_HOOK",
      "dll": "acrobat_hook.dll",
      "success_rate": 92
    },
    {
      "type": "PROCESS_CHECK",
      "method": "PROCESS_HIDING",
      "hide_from_apis": ["CreateToolhelp32Snapshot", "EnumProcesses"],
      "success_rate": 88
    }
  ]
}
```

### 游戏和娱乐软件

#### Steam客户端
```json
{
  "name": "Steam Client",
  "executable": "steam.exe",
  "restrictions": ["MUTEX_LOCK", "NETWORK_PORT", "REGISTRY_CHECK"],
  "known_mutexes": ["SteamClientMutex", "SteamAppMutex"],
  "bypass_strategies": [
    {
      "type": "NETWORK_PORT",
      "method": "PORT_VIRTUALIZATION",
      "default_ports": [27015, 27016, 27017],
      "success_rate": 90
    },
    {
      "type": "REGISTRY_CHECK",
      "method": "REGISTRY_REDIRECTION",
      "base_key": "HKCU\\Software\\Valve\\Steam",
      "success_rate": 85
    }
  ],
  "launch_parameters": "-no-browser +clientport {PORT}",
  "special_notes": "每个实例需要不同的客户端端口"
}
```

### 开发工具

#### Visual Studio Code
```json
{
  "name": "Visual Studio Code",
  "executable": "Code.exe",
  "restrictions": ["MUTEX_LOCK", "FILE_LOCK", "NAMED_PIPE"],
  "bypass_strategies": [
    {
      "type": "NAMED_PIPE",
      "method": "PIPE_REDIRECTION",
      "pipe_names": ["vscode-ipc", "vscode-git"],
      "success_rate": 95
    }
  ],
  "launch_parameters": "--user-data-dir=\"%TEMP%\\VSCode\\{PID}\"",
  "success_rate": 98,
  "notes": "VSCode支持多实例，主要通过用户数据目录隔离"
}
```

### 配置文件详细格式

#### 主配置文件 (config/main_config.json)
```json
{
  "application": {
    "name": "万能多开器",
    "version": "1.0.0",
    "build": "20231201",
    "debug_mode": false
  },
  "detection": {
    "enable_static_analysis": true,
    "enable_dynamic_analysis": true,
    "enable_ml_classification": true,
    "enable_rule_engine": true,
    "confidence_threshold": 0.7,
    "fallback_threshold": 0.5,
    "max_analysis_time": 30000,
    "analysis_timeout": 60000
  },
  "bypass": {
    "default_strategy": "AUTO",
    "max_instances": 10,
    "instance_delay": 1000,
    "cleanup_on_exit": true,
    "stealth_mode": true,
    "anti_detection": true
  },
  "gui": {
    "theme": "auto",
    "language": "zh-CN",
    "window_size": {
      "width": 800,
      "height": 600,
      "min_width": 600,
      "min_height": 400
    },
    "auto_analyze": true,
    "show_advanced_options": false,
    "log_level": "INFO"
  },
  "security": {
    "require_admin": false,
    "verify_signatures": true,
    "sandbox_mode": false,
    "whitelist_only": false,
    "max_file_size": 104857600
  },
  "performance": {
    "max_memory_usage": 1073741824,
    "cpu_limit": 80,
    "enable_monitoring": true,
    "auto_cleanup": true,
    "cache_analysis_results": true,
    "cache_expiry": 86400
  },
  "logging": {
    "enable_file_logging": true,
    "log_file_path": "logs/multilauncher.log",
    "max_log_size": 10485760,
    "log_rotation": true,
    "log_level": "INFO",
    "enable_debug_logging": false
  },
  "updates": {
    "check_for_updates": true,
    "auto_update": false,
    "update_channel": "stable",
    "update_server": "https://updates.example.com"
  }
}
```

#### 应用程序配置文件 (config/app_profiles.json)
```json
{
  "version": "1.0",
  "last_updated": "2023-12-01T10:00:00Z",
  "profiles": [
    {
      "id": "test_app_001",
      "name": "测试应用程序",
      "executable": "testapp.exe",
      "version_range": "1.0-2.0",
      "publisher": "Test Company",
      "description": "用于测试的示例应用程序",
      "category": "测试工具",
      "restrictions": [
        {
          "type": "MUTEX_LOCK",
          "confidence": 0.95,
          "evidence": ["CreateMutexA", "OpenMutexA"],
          "mutex_names": ["TestAppMutex", "TestApp_SingleInstance"]
        },
        {
          "type": "FILE_LOCK",
          "confidence": 0.87,
          "evidence": ["CreateFileA with GENERIC_WRITE"],
          "locked_files": ["config.ini", "app.lock"]
        }
      ],
      "bypass_strategies": [
        {
          "id": "strategy_001",
          "type": "MUTEX_LOCK",
          "method": "API_HOOK",
          "priority": 1,
          "success_rate": 0.95,
          "dll": "mutex_hook.dll",
          "parameters": {
            "hook_functions": ["CreateMutexA", "CreateMutexW", "OpenMutexA", "OpenMutexW"],
            "name_modification": "append_pid",
            "stealth_mode": true
          },
          "requirements": ["admin_rights"],
          "compatibility": ["Windows 7", "Windows 10", "Windows 11"],
          "notes": "标准Mutex Hook策略，适用于大多数应用"
        },
        {
          "id": "strategy_002",
          "type": "FILE_LOCK",
          "method": "FILE_REDIRECT",
          "priority": 2,
          "success_rate": 0.90,
          "parameters": {
            "redirect_base": "%TEMP%\\MultiLauncher\\{APP_NAME}\\{PID}",
            "files_to_redirect": ["config.ini", "app.lock", "temp.dat"],
            "copy_original": true,
            "cleanup_on_exit": true
          },
          "notes": "将锁定文件重定向到临时目录"
        }
      ],
      "launch_parameters": {
        "default": "",
        "silent": "/silent",
        "windowed": "/windowed",
        "custom": "/instance:{INSTANCE_ID}"
      },
      "environment_variables": {
        "APP_INSTANCE_ID": "{PID}",
        "APP_TEMP_DIR": "%TEMP%\\MultiLauncher\\{PID}"
      },
      "post_launch_actions": [
        {
          "action": "wait_for_window",
          "parameters": {
            "window_class": "TestAppMainWindow",
            "timeout": 10000
          }
        },
        {
          "action": "inject_dll",
          "parameters": {
            "dll_path": "hooks\\runtime_hook.dll",
            "injection_method": "CreateRemoteThread"
          }
        }
      ],
      "known_issues": [
        {
          "issue": "在Windows 7上可能需要额外的权限",
          "severity": "medium",
          "workaround": "以管理员身份运行"
        }
      ],
      "testing_info": {
        "last_tested": "2023-11-30",
        "tested_versions": ["1.0", "1.5", "2.0"],
        "test_results": {
          "success_rate": 0.92,
          "average_launch_time": 2500,
          "max_instances_tested": 5
        }
      }
    }
  ],
  "global_settings": {
    "default_instance_limit": 5,
    "default_timeout": 30000,
    "enable_auto_detection": true,
    "fallback_strategies": ["PROCESS_ISOLATION", "SANDBOX_MODE"]
  }
}
```

#### 规则引擎配置 (config/detection_rules.json)
```json
{
  "version": "1.0",
  "rules": [
    {
      "id": "rule_mutex_001",
      "name": "标准Mutex检测",
      "description": "检测使用CreateMutex API的单实例限制",
      "category": "MUTEX_DETECTION",
      "priority": 10,
      "enabled": true,
      "conditions": [
        {
          "type": "API_IMPORT",
          "pattern": "CreateMutex[AW]",
          "weight": 0.8,
          "required": true
        },
        {
          "type": "STRING_PATTERN",
          "pattern": ".*Mutex.*",
          "weight": 0.3,
          "required": false
        },
        {
          "type": "STRING_PATTERN",
          "pattern": ".*SingleInstance.*",
          "weight": 0.5,
          "required": false
        }
      ],
      "threshold": 0.7,
      "output": {
        "restriction_type": "MUTEX_LOCK",
        "confidence_base": 0.85,
        "recommended_strategies": ["API_HOOK", "MUTEX_RENAME"]
      }
    },
    {
      "id": "rule_file_001",
      "name": "文件锁定检测",
      "description": "检测独占文件访问限制",
      "category": "FILE_DETECTION",
      "priority": 8,
      "enabled": true,
      "conditions": [
        {
          "type": "API_IMPORT",
          "pattern": "CreateFile[AW]",
          "weight": 0.6,
          "required": true
        },
        {
          "type": "STRING_PATTERN",
          "pattern": ".*\\.lock",
          "weight": 0.4,
          "required": false
        },
        {
          "type": "BEHAVIOR_PATTERN",
          "pattern": "exclusive_file_access",
          "weight": 0.7,
          "required": false
        }
      ],
      "threshold": 0.6,
      "output": {
        "restriction_type": "FILE_LOCK",
        "confidence_base": 0.75,
        "recommended_strategies": ["FILE_REDIRECT", "FILE_VIRTUALIZATION"]
      }
    }
  ],
  "global_settings": {
    "enable_machine_learning": true,
    "ml_weight": 0.4,
    "rule_weight": 0.6,
    "min_confidence": 0.5,
    "max_rules_per_scan": 50
  }
}
```

## 调试指南扩展

### 专业调试工具集

#### 1. 系统监控工具
- **Process Monitor (ProcMon)** - 实时监控文件系统、注册表和进程/线程活动
  - 过滤器设置：进程名、操作类型、路径模式
  - 导出功能：CSV、XML格式，便于后续分析
  - 高级功能：堆栈跟踪、进程树视图

- **API Monitor** - 监控和显示API调用
  - 支持32位和64位进程
  - 可自定义API监控列表
  - 实时显示参数和返回值
  - 断点和条件监控功能

- **Dependency Walker** - 分析PE文件的DLL依赖关系
  - 检测缺失的DLL
  - 分析导入/导出函数
  - 识别循环依赖问题

#### 2. 动态调试工具
- **x64dbg/x32dbg** - 现代化的Windows调试器
  - 插件系统支持
  - 脚本自动化功能
  - 内存搜索和补丁功能
  - 反汇编和十六进制编辑

- **Cheat Engine** - 内存扫描和修改工具
  - 内存搜索和过滤
  - 代码注入和Hook
  - 调试器功能
  - Lua脚本支持

- **WinAPIOverride** - API监控和重定向工具
  - 实时API监控
  - 参数修改和返回值伪造
  - 自定义DLL注入
  - 日志记录和分析

#### 3. 网络和通信调试
- **Wireshark** - 网络协议分析器
  - 捕获和分析网络流量
  - 过滤和搜索功能
  - 协议解析和统计

- **TCPView** - 显示网络连接和端口使用情况
  - 实时监控TCP/UDP连接
  - 进程关联显示
  - 连接状态跟踪

### 复杂场景调试方案

#### 场景1：加壳程序调试
```cpp
// 加壳检测和处理
class PackerDetector {
public:
    enum PackerType {
        UPX,
        ASPACK,
        PECOMPACT,
        THEMIDA,
        VMPROTECT,
        UNKNOWN
    };

    PackerType DetectPacker(const std::string& exePath) {
        // 1. 检查PE节名称
        std::vector<std::string> sectionNames = GetSectionNames(exePath);

        // UPX特征
        if (std::find(sectionNames.begin(), sectionNames.end(), "UPX0") != sectionNames.end()) {
            return UPX;
        }

        // ASPack特征
        if (std::find(sectionNames.begin(), sectionNames.end(), ".aspack") != sectionNames.end()) {
            return ASPACK;
        }

        // 2. 检查导入表特征
        std::vector<std::string> imports = GetImportFunctions(exePath);
        if (imports.size() < 10) {
            // 导入函数过少，可能被加壳
            return UNKNOWN;
        }

        // 3. 检查入口点代码
        std::vector<BYTE> entryCode = GetEntryPointCode(exePath);
        if (IsPackedCode(entryCode)) {
            return UNKNOWN;
        }

        return UNKNOWN;
    }

    bool UnpackProgram(const std::string& exePath, PackerType type) {
        switch (type) {
            case UPX:
                return UnpackUPX(exePath);
            case ASPACK:
                return UnpackASPack(exePath);
            default:
                return GenericUnpack(exePath);
        }
    }

private:
    bool UnpackUPX(const std::string& exePath) {
        // 使用UPX命令行工具解包
        std::string command = "upx -d \"" + exePath + "\"";
        return system(command.c_str()) == 0;
    }

    bool GenericUnpack(const std::string& exePath) {
        // 通用脱壳方法：内存dump
        DWORD processId = LaunchSuspended(exePath);
        if (processId == 0) return false;

        // 等待OEP到达
        PVOID oep = WaitForOEP(processId);
        if (oep == nullptr) {
            TerminateProcess(processId);
            return false;
        }

        // Dump内存到文件
        bool success = DumpProcessMemory(processId, exePath + ".unpacked");
        TerminateProcess(processId);

        return success;
    }
};
```

#### 场景2：反调试程序处理
```cpp
// 反调试检测和绕过
class AntiDebugHandler {
public:
    struct AntiDebugInfo {
        std::vector<std::string> detectedTechniques;
        std::vector<PVOID> patchAddresses;
        bool isProtected;
        std::string protectionLevel;
    };

    AntiDebugInfo AnalyzeAntiDebug(const std::string& exePath) {
        AntiDebugInfo info;

        // 1. 静态分析反调试API
        std::vector<std::string> antiDebugAPIs = {
            "IsDebuggerPresent",
            "CheckRemoteDebuggerPresent",
            "NtQueryInformationProcess",
            "GetTickCount",
            "QueryPerformanceCounter",
            "OutputDebugStringA"
        };

        for (const auto& api : antiDebugAPIs) {
            if (HasImport(exePath, api)) {
                info.detectedTechniques.push_back("API: " + api);
            }
        }

        // 2. 检测反调试字符串
        std::vector<std::string> antiDebugStrings = {
            "debugger",
            "ollydbg",
            "x64dbg",
            "ida",
            "windbg"
        };

        for (const auto& str : antiDebugStrings) {
            if (HasString(exePath, str)) {
                info.detectedTechniques.push_back("String: " + str);
            }
        }

        // 3. 动态分析
        DWORD processId = LaunchForAnalysis(exePath);
        if (processId != 0) {
            // 监控反调试行为
            MonitorAntiDebugBehavior(processId, info);
            TerminateProcess(processId);
        }

        // 评估保护级别
        if (info.detectedTechniques.size() > 10) {
            info.protectionLevel = "高";
        } else if (info.detectedTechniques.size() > 5) {
            info.protectionLevel = "中";
        } else {
            info.protectionLevel = "低";
        }

        info.isProtected = !info.detectedTechniques.empty();

        return info;
    }

    bool BypassAntiDebug(const std::string& exePath, const AntiDebugInfo& info) {
        // 1. 创建补丁文件
        std::string patchedPath = exePath + ".patched";
        if (!CopyFileA(exePath.c_str(), patchedPath.c_str(), FALSE)) {
            return false;
        }

        // 2. 应用补丁
        for (const auto& technique : info.detectedTechniques) {
            if (technique.find("IsDebuggerPresent") != std::string::npos) {
                PatchIsDebuggerPresent(patchedPath);
            } else if (technique.find("NtQueryInformationProcess") != std::string::npos) {
                PatchNtQueryInformationProcess(patchedPath);
            }
            // ... 其他补丁
        }

        return true;
    }

private:
    bool PatchIsDebuggerPresent(const std::string& filePath) {
        // 将IsDebuggerPresent调用替换为返回FALSE
        // 搜索调用模式：FF 15 xx xx xx xx (call dword ptr [xxxxxxxx])
        std::vector<BYTE> pattern = {0xFF, 0x15};
        std::vector<BYTE> replacement = {0x33, 0xC0, 0x90, 0x90, 0x90, 0x90}; // xor eax,eax; nop*4

        return PatchBinaryFile(filePath, pattern, replacement);
    }

    bool PatchBinaryFile(const std::string& filePath,
                        const std::vector<BYTE>& pattern,
                        const std::vector<BYTE>& replacement) {
        // 实现二进制文件补丁功能
        HANDLE hFile = CreateFileA(filePath.c_str(), GENERIC_READ | GENERIC_WRITE,
                                  0, NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
        if (hFile == INVALID_HANDLE_VALUE) return false;

        DWORD fileSize = GetFileSize(hFile, NULL);
        std::vector<BYTE> fileData(fileSize);

        DWORD bytesRead;
        ReadFile(hFile, fileData.data(), fileSize, &bytesRead, NULL);

        // 搜索和替换模式
        bool patched = false;
        for (size_t i = 0; i <= fileData.size() - pattern.size(); i++) {
            if (std::equal(pattern.begin(), pattern.end(), fileData.begin() + i)) {
                std::copy(replacement.begin(), replacement.end(), fileData.begin() + i);
                patched = true;
            }
        }

        if (patched) {
            SetFilePointer(hFile, 0, NULL, FILE_BEGIN);
            DWORD bytesWritten;
            WriteFile(hFile, fileData.data(), fileSize, &bytesWritten, NULL);
        }

        CloseHandle(hFile);
        return patched;
    }
};
```

#### 场景3：虚拟化保护程序
```cpp
// 虚拟化保护检测和处理
class VirtualizationHandler {
public:
    enum VirtualizationType {
        VMPROTECT,
        THEMIDA,
        ENIGMA,
        CODE_VIRTUALIZER,
        UNKNOWN_VM
    };

    VirtualizationType DetectVirtualization(const std::string& exePath) {
        // 1. 检查特征字符串
        if (HasString(exePath, "VMProtect")) return VMPROTECT;
        if (HasString(exePath, "Themida")) return THEMIDA;
        if (HasString(exePath, "Enigma")) return ENIGMA;

        // 2. 检查代码特征
        std::vector<BYTE> entryCode = GetEntryPointCode(exePath);
        if (IsVMProtectCode(entryCode)) return VMPROTECT;
        if (IsThemidaCode(entryCode)) return THEMIDA;

        // 3. 检查节结构
        std::vector<std::string> sections = GetSectionNames(exePath);
        for (const auto& section : sections) {
            if (section.find(".vmp") != std::string::npos) return VMPROTECT;
            if (section.find(".tmd") != std::string::npos) return THEMIDA;
        }

        return UNKNOWN_VM;
    }

    bool HandleVirtualizedProgram(const std::string& exePath, VirtualizationType type) {
        switch (type) {
            case VMPROTECT:
                return HandleVMProtect(exePath);
            case THEMIDA:
                return HandleThemida(exePath);
            default:
                return HandleGenericVM(exePath);
        }
    }

private:
    bool HandleVMProtect(const std::string& exePath) {
        // VMProtect特殊处理
        // 1. 使用内存断点跟踪
        // 2. 识别VM入口和出口
        // 3. 重建原始代码
        return true;
    }

    bool HandleGenericVM(const std::string& exePath) {
        // 通用虚拟化处理
        // 1. 动态分析VM行为
        // 2. 记录指令执行轨迹
        // 3. 尝试代码重建
        return true;
    }
};
```

### 异常恢复方案

#### 内存泄漏检测和修复
```cpp
// 内存泄漏检测器
class MemoryLeakDetector {
private:
    struct AllocationInfo {
        PVOID address;
        SIZE_T size;
        std::string source;
        DWORD timestamp;
        std::vector<PVOID> callStack;
    };

    std::map<PVOID, AllocationInfo> allocations;
    CRITICAL_SECTION cs;
    bool isEnabled;

public:
    MemoryLeakDetector() : isEnabled(false) {
        InitializeCriticalSection(&cs);
    }

    ~MemoryLeakDetector() {
        DeleteCriticalSection(&cs);
    }

    void Enable() {
        EnterCriticalSection(&cs);
        isEnabled = true;

        // Hook内存分配函数
        HookMemoryFunctions();

        LeaveCriticalSection(&cs);
    }

    void Disable() {
        EnterCriticalSection(&cs);
        isEnabled = false;

        // 恢复原始函数
        UnhookMemoryFunctions();

        LeaveCriticalSection(&cs);
    }

    void RecordAllocation(PVOID address, SIZE_T size, const std::string& source) {
        if (!isEnabled) return;

        EnterCriticalSection(&cs);

        AllocationInfo info;
        info.address = address;
        info.size = size;
        info.source = source;
        info.timestamp = GetTickCount();
        info.callStack = CaptureCallStack();

        allocations[address] = info;

        LeaveCriticalSection(&cs);
    }

    void RecordDeallocation(PVOID address) {
        if (!isEnabled) return;

        EnterCriticalSection(&cs);
        allocations.erase(address);
        LeaveCriticalSection(&cs);
    }

    std::vector<AllocationInfo> DetectLeaks() {
        std::vector<AllocationInfo> leaks;

        EnterCriticalSection(&cs);

        DWORD currentTime = GetTickCount();
        for (const auto& pair : allocations) {
            const AllocationInfo& info = pair.second;

            // 超过5分钟未释放的内存视为泄漏
            if (currentTime - info.timestamp > 300000) {
                leaks.push_back(info);
            }
        }

        LeaveCriticalSection(&cs);

        return leaks;
    }

    bool FixLeaks() {
        std::vector<AllocationInfo> leaks = DetectLeaks();

        for (const auto& leak : leaks) {
            // 尝试释放泄漏的内存
            if (IsSafeToFree(leak.address)) {
                free(leak.address);
                RecordDeallocation(leak.address);
            }
        }

        return true;
    }

private:
    std::vector<PVOID> CaptureCallStack() {
        std::vector<PVOID> callStack;

        // 使用CaptureStackBackTrace捕获调用栈
        PVOID stack[16];
        USHORT frames = CaptureStackBackTrace(0, 16, stack, NULL);

        for (USHORT i = 0; i < frames; i++) {
            callStack.push_back(stack[i]);
        }

        return callStack;
    }

    bool IsSafeToFree(PVOID address) {
        // 检查内存是否可以安全释放
        MEMORY_BASIC_INFORMATION mbi;
        if (VirtualQuery(address, &mbi, sizeof(mbi)) == 0) {
            return false;
        }

        // 检查内存状态
        return (mbi.State == MEM_COMMIT) &&
               (mbi.Type == MEM_PRIVATE) &&
               (mbi.Protect & PAGE_READWRITE);
    }
};
```

### 常见问题解决方案

#### 1. DLL注入失败的完整诊断
```cpp
// DLL注入失败诊断器
class InjectionDiagnostic {
public:
    enum FailureReason {
        PROCESS_NOT_FOUND,
        INSUFFICIENT_PRIVILEGES,
        ARCHITECTURE_MISMATCH,
        DLL_NOT_FOUND,
        DLL_LOAD_FAILED,
        REMOTE_THREAD_FAILED,
        HOOK_DETECTION,
        ANTIVIRUS_BLOCKED,
        UNKNOWN_ERROR
    };

    FailureReason DiagnoseInjectionFailure(DWORD processId, const std::string& dllPath) {
        // 1. 检查进程是否存在
        HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, processId);
        if (hProcess == NULL) {
            return PROCESS_NOT_FOUND;
        }

        // 2. 检查权限
        HANDLE hProcessFull = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (hProcessFull == NULL) {
            CloseHandle(hProcess);
            return INSUFFICIENT_PRIVILEGES;
        }

        // 3. 检查架构匹配
        BOOL isWow64Process, isWow64Current;
        IsWow64Process(hProcess, &isWow64Process);
        IsWow64Process(GetCurrentProcess(), &isWow64Current);

        if (isWow64Process != isWow64Current) {
            CloseHandle(hProcess);
            CloseHandle(hProcessFull);
            return ARCHITECTURE_MISMATCH;
        }

        // 4. 检查DLL文件
        if (GetFileAttributesA(dllPath.c_str()) == INVALID_FILE_ATTRIBUTES) {
            CloseHandle(hProcess);
            CloseHandle(hProcessFull);
            return DLL_NOT_FOUND;
        }

        // 5. 尝试在目标进程中加载DLL
        PVOID remotePath = VirtualAllocEx(hProcessFull, NULL, dllPath.length() + 1,
                                         MEM_COMMIT, PAGE_READWRITE);
        if (remotePath == NULL) {
            CloseHandle(hProcess);
            CloseHandle(hProcessFull);
            return UNKNOWN_ERROR;
        }

        WriteProcessMemory(hProcessFull, remotePath, dllPath.c_str(),
                          dllPath.length() + 1, NULL);

        HANDLE hThread = CreateRemoteThread(hProcessFull, NULL, 0,
                                           (LPTHREAD_START_ROUTINE)LoadLibraryA,
                                           remotePath, 0, NULL);

        if (hThread == NULL) {
            VirtualFreeEx(hProcessFull, remotePath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            CloseHandle(hProcessFull);
            return REMOTE_THREAD_FAILED;
        }

        // 等待加载完成
        WaitForSingleObject(hThread, 5000);

        DWORD exitCode;
        GetExitCodeThread(hThread, &exitCode);

        CloseHandle(hThread);
        VirtualFreeEx(hProcessFull, remotePath, 0, MEM_RELEASE);
        CloseHandle(hProcess);
        CloseHandle(hProcessFull);

        if (exitCode == 0) {
            return DLL_LOAD_FAILED;
        }

        return UNKNOWN_ERROR;
    }

    std::string GetFailureDescription(FailureReason reason) {
        switch (reason) {
            case PROCESS_NOT_FOUND:
                return "目标进程不存在或已退出";
            case INSUFFICIENT_PRIVILEGES:
                return "权限不足，请以管理员身份运行";
            case ARCHITECTURE_MISMATCH:
                return "架构不匹配（32位/64位）";
            case DLL_NOT_FOUND:
                return "DLL文件不存在";
            case DLL_LOAD_FAILED:
                return "DLL加载失败，可能缺少依赖";
            case REMOTE_THREAD_FAILED:
                return "远程线程创建失败";
            case HOOK_DETECTION:
                return "目标程序检测到Hook尝试";
            case ANTIVIRUS_BLOCKED:
                return "被杀毒软件阻止";
            default:
                return "未知错误";
        }
    }
};
```

#### 2. Hook失败的详细分析
```cpp
// Hook失败分析器
class HookDiagnostic {
public:
    enum HookFailureReason {
        API_NOT_FOUND,
        ALREADY_HOOKED,
        MEMORY_PROTECTION,
        INSUFFICIENT_SPACE,
        INVALID_INSTRUCTION,
        ANTIHOOK_PROTECTION,
        UNKNOWN_HOOK_ERROR
    };

    HookFailureReason DiagnoseHookFailure(const std::string& moduleName,
                                         const std::string& functionName) {
        // 1. 检查API是否存在
        HMODULE hModule = GetModuleHandleA(moduleName.c_str());
        if (hModule == NULL) {
            hModule = LoadLibraryA(moduleName.c_str());
            if (hModule == NULL) {
                return API_NOT_FOUND;
            }
        }

        FARPROC apiAddress = GetProcAddress(hModule, functionName.c_str());
        if (apiAddress == NULL) {
            return API_NOT_FOUND;
        }

        // 2. 检查是否已被Hook
        if (IsAlreadyHooked(apiAddress)) {
            return ALREADY_HOOKED;
        }

        // 3. 检查内存保护
        MEMORY_BASIC_INFORMATION mbi;
        VirtualQuery(apiAddress, &mbi, sizeof(mbi));

        if (!(mbi.Protect & PAGE_EXECUTE_READWRITE) &&
            !(mbi.Protect & PAGE_EXECUTE_WRITECOPY)) {
            return MEMORY_PROTECTION;
        }

        // 4. 检查指令空间
        if (!HasSufficientSpace(apiAddress)) {
            return INSUFFICIENT_SPACE;
        }

        // 5. 检查指令有效性
        if (!IsValidInstructionSequence(apiAddress)) {
            return INVALID_INSTRUCTION;
        }

        // 6. 检查反Hook保护
        if (HasAntiHookProtection(apiAddress)) {
            return ANTIHOOK_PROTECTION;
        }

        return UNKNOWN_HOOK_ERROR;
    }

private:
    bool IsAlreadyHooked(FARPROC apiAddress) {
        BYTE* code = (BYTE*)apiAddress;

        // 检查常见的Hook特征
        // JMP指令 (E9 xx xx xx xx)
        if (code[0] == 0xE9) return true;

        // PUSH + RET组合
        if (code[0] == 0x68 && code[5] == 0xC3) return true;

        // MOV EAX + JMP EAX
        if (code[0] == 0xB8 && code[5] == 0xFF && code[6] == 0xE0) return true;

        return false;
    }

    bool HasSufficientSpace(FARPROC apiAddress) {
        // 检查是否有足够的空间安装Hook（至少5字节）
        BYTE* code = (BYTE*)apiAddress;

        // 简单检查：确保前5个字节不会破坏指令边界
        // 这里需要更复杂的反汇编逻辑
        return true;
    }

    bool IsValidInstructionSequence(FARPROC apiAddress) {
        // 检查指令序列是否有效
        // 需要反汇编引擎支持
        return true;
    }

    bool HasAntiHookProtection(FARPROC apiAddress) {
        // 检查是否有反Hook保护
        // 例如：代码完整性检查、CRC校验等
        return false;
    }
};
```

#### 3. 权限问题的自动解决
```cpp
// 权限管理器
class PrivilegeManager {
public:
    bool ElevatePrivileges() {
        // 1. 检查当前权限级别
        if (IsRunningAsAdmin()) {
            return true;
        }

        // 2. 尝试获取调试权限
        if (EnableDebugPrivilege()) {
            return true;
        }

        // 3. 请求UAC提升
        return RequestUACElevation();
    }

    bool IsRunningAsAdmin() {
        BOOL isAdmin = FALSE;
        PSID adminGroup = NULL;

        SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;

        if (AllocateAndInitializeSid(&ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                    DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup)) {
            CheckTokenMembership(NULL, adminGroup, &isAdmin);
            FreeSid(adminGroup);
        }

        return isAdmin == TRUE;
    }

    bool EnableDebugPrivilege() {
        HANDLE hToken;
        TOKEN_PRIVILEGES tp;

        if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES, &hToken)) {
            return false;
        }

        tp.PrivilegeCount = 1;
        LookupPrivilegeValue(NULL, SE_DEBUG_NAME, &tp.Privileges[0].Luid);
        tp.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;

        AdjustTokenPrivileges(hToken, FALSE, &tp, sizeof(tp), NULL, NULL);
        CloseHandle(hToken);

        return GetLastError() == ERROR_SUCCESS;
    }

    bool RequestUACElevation() {
        // 重新启动程序并请求管理员权限
        char exePath[MAX_PATH];
        GetModuleFileNameA(NULL, exePath, MAX_PATH);

        SHELLEXECUTEINFOA sei = {0};
        sei.cbSize = sizeof(sei);
        sei.lpVerb = "runas";
        sei.lpFile = exePath;
        sei.hwnd = NULL;
        sei.nShow = SW_NORMAL;

        return ShellExecuteExA(&sei) == TRUE;
    }
};
```

## 性能优化

### 1. 检测优化
- 缓存PE分析结果
- 并行化检测过程
- 智能跳过已知安全的API

### 2. 注入优化
- 预编译Hook DLL
- 使用内存池管理
- 延迟加载非关键模块

### 3. 内存优化
- 及时释放不需要的资源
- 使用智能指针管理内存
- 避免内存泄漏

## 安全考虑

### 1. 代码签名
```batch
# 对编译后的文件进行签名
signtool sign /f certificate.pfx /p password /t http://timestamp.server.com MultiLauncher.exe
```

### 2. 反病毒误报
- 使用白名单技术
- 提交样本到杀毒厂商
- 添加数字签名

### 3. 权限控制
- 最小权限原则
- 用户确认机制
- 审计日志记录

## 扩展开发

### 添加新的检测类型
```cpp
// 1. 在RestrictionType枚举中添加新类型
enum class RestrictionType {
    // ... 现有类型
    NEW_RESTRICTION_TYPE
};

// 2. 在RestrictionDetector中添加检测逻辑
bool DetectNewRestriction(const string& exePath, DetectionResult& result);

// 3. 在BypassEngine中添加绕过策略
bool BypassNewRestriction(const string& exePath, const DetectionResult& detection);
```

### 添加新的绕过方法
```cpp
// 1. 创建新的Hook DLL
// src/hooks/new_hook.cpp

// 2. 在BypassEngine中注册新方法
bool RegisterNewBypassMethod(const string& methodName, BypassFunction func);

// 3. 更新配置文件格式
```

## 完整的部署和分发指南

### 编译和打包流程

#### 1. 预编译准备
```batch
# 创建发布目录结构
mkdir release
mkdir release\bin
mkdir release\hooks
mkdir release\config
mkdir release\docs
mkdir release\logs
mkdir release\temp

# 复制必要的依赖库
copy libs\detours\lib.X64\detours.lib release\bin\
copy libs\sqlite\sqlite3.dll release\bin\
copy "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Redist\MSVC\14.29.30133\x64\Microsoft.VC142.CRT\*.dll" release\bin\
```

#### 2. 自动化编译脚本
```batch
@echo off
echo 万能多开器自动编译脚本
echo ========================

set BUILD_CONFIG=Release
set BUILD_PLATFORM=x64
set VERSION=1.0.0
set BUILD_DATE=%date:~0,4%%date:~5,2%%date:~8,2%

echo 编译配置: %BUILD_CONFIG%
echo 目标平台: %BUILD_PLATFORM%
echo 版本号: %VERSION%
echo 编译日期: %BUILD_DATE%

echo.
echo 1. 清理旧的编译文件...
if exist build rmdir /s /q build
mkdir build

echo 2. 生成项目文件...
cd build
cmake .. -G "Visual Studio 16 2019" -A %BUILD_PLATFORM%
if errorlevel 1 goto error

echo 3. 编译主程序...
cmake --build . --config %BUILD_CONFIG% --target MultiLauncher
if errorlevel 1 goto error

echo 4. 编译Hook DLL...
cmake --build . --config %BUILD_CONFIG% --target mutex_hook
cmake --build . --config %BUILD_CONFIG% --target file_hook
cmake --build . --config %BUILD_CONFIG% --target registry_hook
cmake --build . --config %BUILD_CONFIG% --target window_hook
cmake --build . --config %BUILD_CONFIG% --target process_hook
cmake --build . --config %BUILD_CONFIG% --target memory_hook
cmake --build . --config %BUILD_CONFIG% --target stealth_hook
if errorlevel 1 goto error

echo 5. 运行单元测试...
cmake --build . --config %BUILD_CONFIG% --target RUN_TESTS
if errorlevel 1 echo 警告: 部分测试失败，但继续编译过程

echo 6. 创建发布包...
cd ..
call scripts\create_release_package.bat %VERSION% %BUILD_DATE%
if errorlevel 1 goto error

echo.
echo 编译完成！发布包位于: release\MultiLauncher_%VERSION%_%BUILD_DATE%.zip
goto end

:error
echo.
echo 编译失败！请检查错误信息。
pause
exit /b 1

:end
echo.
echo 按任意键退出...
pause
```

#### 3. 发布包创建脚本
```batch
@echo off
set VERSION=%1
set BUILD_DATE=%2
set PACKAGE_NAME=MultiLauncher_%VERSION%_%BUILD_DATE%

echo 创建发布包: %PACKAGE_NAME%

# 创建临时打包目录
mkdir temp_package
mkdir temp_package\bin
mkdir temp_package\hooks
mkdir temp_package\config
mkdir temp_package\docs
mkdir temp_package\examples

# 复制主程序
copy build\Release\MultiLauncher.exe temp_package\bin\
copy build\Release\*.pdb temp_package\bin\ 2>nul

# 复制Hook DLL
copy build\hooks\Release\*.dll temp_package\hooks\

# 复制配置文件
copy config\*.json temp_package\config\
copy config\*.xml temp_package\config\

# 复制文档
copy docs\*.md temp_package\docs\
copy README.md temp_package\
copy LICENSE temp_package\

# 复制示例程序
copy test_apps\*.exe temp_package\examples\

# 创建版本信息文件
echo Version: %VERSION% > temp_package\VERSION.txt
echo Build Date: %BUILD_DATE% >> temp_package\VERSION.txt
echo Build Configuration: Release >> temp_package\VERSION.txt
echo Target Platform: x64 >> temp_package\VERSION.txt

# 创建安装脚本
echo @echo off > temp_package\install.bat
echo echo 万能多开器安装程序 >> temp_package\install.bat
echo echo ================== >> temp_package\install.bat
echo. >> temp_package\install.bat
echo if not exist "C:\Program Files\MultiLauncher" mkdir "C:\Program Files\MultiLauncher" >> temp_package\install.bat
echo xcopy /s /y bin "C:\Program Files\MultiLauncher\bin\" >> temp_package\install.bat
echo xcopy /s /y hooks "C:\Program Files\MultiLauncher\hooks\" >> temp_package\install.bat
echo xcopy /s /y config "C:\Program Files\MultiLauncher\config\" >> temp_package\install.bat
echo xcopy /s /y docs "C:\Program Files\MultiLauncher\docs\" >> temp_package\install.bat
echo echo 安装完成！ >> temp_package\install.bat
echo pause >> temp_package\install.bat

# 创建卸载脚本
echo @echo off > temp_package\uninstall.bat
echo echo 万能多开器卸载程序 >> temp_package\uninstall.bat
echo echo ================== >> temp_package\uninstall.bat
echo. >> temp_package\uninstall.bat
echo if exist "C:\Program Files\MultiLauncher" rmdir /s /q "C:\Program Files\MultiLauncher" >> temp_package\uninstall.bat
echo echo 卸载完成！ >> temp_package\uninstall.bat
echo pause >> temp_package\uninstall.bat

# 创建ZIP包
powershell Compress-Archive -Path temp_package\* -DestinationPath release\%PACKAGE_NAME%.zip -Force

# 清理临时目录
rmdir /s /q temp_package

echo 发布包创建完成: release\%PACKAGE_NAME%.zip
```

### 数字签名和安全

#### 1. 代码签名流程
```batch
# 使用signtool对所有可执行文件进行签名
set CERT_FILE=certificates\code_signing.pfx
set CERT_PASSWORD=your_password
set TIMESTAMP_SERVER=http://timestamp.digicert.com

echo 对主程序进行数字签名...
signtool sign /f %CERT_FILE% /p %CERT_PASSWORD% /t %TIMESTAMP_SERVER% /d "万能多开器" /du "https://github.com/username/universal-multi-launcher" temp_package\bin\MultiLauncher.exe

echo 对Hook DLL进行数字签名...
for %%f in (temp_package\hooks\*.dll) do (
    signtool sign /f %CERT_FILE% /p %CERT_PASSWORD% /t %TIMESTAMP_SERVER% /d "万能多开器Hook模块" "%%f"
)

echo 验证数字签名...
signtool verify /pa temp_package\bin\MultiLauncher.exe
if errorlevel 1 (
    echo 数字签名验证失败！
    exit /b 1
)

echo 数字签名完成！
```

#### 2. 病毒扫描和白名单提交
```batch
# 自动化病毒扫描脚本
echo 执行病毒扫描...

# 使用Windows Defender扫描
"%ProgramFiles%\Windows Defender\MpCmdRun.exe" -Scan -ScanType 3 -File "temp_package\bin\MultiLauncher.exe"

# 提交到VirusTotal（需要API密钥）
python scripts\virustotal_submit.py temp_package\bin\MultiLauncher.exe

# 生成扫描报告
echo 病毒扫描完成，请查看报告文件。
```

### 分发渠道和更新机制

#### 1. GitHub Releases自动发布
```yaml
# .github/workflows/release.yml
name: Create Release

on:
  push:
    tags:
      - 'v*'

jobs:
  build-and-release:
    runs-on: windows-latest

    steps:
    - uses: actions/checkout@v2

    - name: Setup MSBuild
      uses: microsoft/setup-msbuild@v1

    - name: Setup NuGet
      uses: NuGet/setup-nuget@v1

    - name: Restore dependencies
      run: nuget restore

    - name: Build solution
      run: msbuild /p:Configuration=Release /p:Platform=x64

    - name: Run tests
      run: vstest.console.exe build\Release\Tests.dll

    - name: Create release package
      run: scripts\create_release_package.bat

    - name: Create Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        draft: false
        prerelease: false

    - name: Upload Release Asset
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ./release/MultiLauncher_${{ github.ref }}.zip
        asset_name: MultiLauncher_${{ github.ref }}.zip
        asset_content_type: application/zip
```

#### 2. 自动更新系统
```cpp
// AutoUpdater.h - 自动更新系统
#ifndef AUTOUPDATER_H
#define AUTOUPDATER_H

#include <string>
#include <vector>
#include <functional>

struct UpdateInfo {
    std::string version;
    std::string downloadUrl;
    std::string changelog;
    std::string releaseDate;
    bool isRequired;
    size_t fileSize;
    std::string checksum;
};

class AutoUpdater {
private:
    std::string updateServerUrl;
    std::string currentVersion;
    std::function<void(int)> progressCallback;
    std::function<void(const std::string&)> statusCallback;

public:
    AutoUpdater(const std::string& serverUrl, const std::string& version);
    ~AutoUpdater();

    // 检查更新
    bool CheckForUpdates(UpdateInfo& updateInfo);

    // 下载更新
    bool DownloadUpdate(const UpdateInfo& updateInfo, const std::string& savePath);

    // 安装更新
    bool InstallUpdate(const std::string& updatePath);

    // 设置回调函数
    void SetProgressCallback(std::function<void(int)> callback);
    void SetStatusCallback(std::function<void(const std::string&)> callback);

    // 验证更新文件
    bool VerifyUpdateFile(const std::string& filePath, const std::string& expectedChecksum);

private:
    // HTTP请求
    std::string HttpGet(const std::string& url);
    bool HttpDownload(const std::string& url, const std::string& savePath);

    // 版本比较
    bool IsNewerVersion(const std::string& remoteVersion, const std::string& localVersion);

    // 计算文件校验和
    std::string CalculateChecksum(const std::string& filePath);

    // 解析更新信息
    UpdateInfo ParseUpdateInfo(const std::string& jsonResponse);
};

#endif
```

### 用户手册和帮助文档

#### 1. 快速入门指南
```markdown
# 万能多开器快速入门指南

## 安装步骤

1. **下载程序**
   - 从GitHub Releases页面下载最新版本
   - 或从官方网站下载安装包

2. **解压安装**
   - 解压下载的ZIP文件到任意目录
   - 推荐安装到：C:\Program Files\MultiLauncher\

3. **首次运行**
   - 右键点击MultiLauncher.exe
   - 选择"以管理员身份运行"
   - 首次运行会自动配置必要的权限

## 基本使用

### 方法一：文件选择
1. 点击"浏览"按钮
2. 选择要多开的程序
3. 设置实例数量
4. 点击"启动多开"

### 方法二：拖拽操作
1. 直接拖拽程序文件到窗口
2. 程序会自动识别并填充路径
3. 设置实例数量并启动

## 常见问题

**Q: 程序提示权限不足怎么办？**
A: 请以管理员身份运行程序，或者在程序图标上右键选择"以管理员身份运行"。

**Q: 某些程序无法多开怎么办？**
A: 点击"分析"按钮查看检测结果，程序会自动选择最佳的绕过策略。

**Q: 程序被杀毒软件误报怎么办？**
A: 这是正常现象，请将程序添加到杀毒软件的白名单中。

## 技术支持

如果遇到问题，请：
1. 查看日志文件（logs目录）
2. 在GitHub Issues页面提交问题
3. 发送邮件到技术支持邮箱
```

#### 2. 高级用户配置指南
```markdown
# 高级配置指南

## 自定义配置文件

### 应用程序配置
编辑 `config/app_profiles.json` 文件可以：
- 添加新的应用程序配置
- 修改现有的绕过策略
- 调整检测参数

### 规则引擎配置
编辑 `config/detection_rules.json` 文件可以：
- 添加自定义检测规则
- 调整规则权重和阈值
- 启用/禁用特定规则

## 命令行参数

```batch
# 静默模式运行
MultiLauncher.exe --silent --instances=3 "C:\Program Files\App\app.exe"

# 使用自定义配置文件
MultiLauncher.exe --config="custom_config.json" "app.exe"

# 启用详细日志
MultiLauncher.exe --verbose --log-level=DEBUG "app.exe"
```

## 插件开发

### Hook DLL开发
1. 创建新的DLL项目
2. 实现Hook函数
3. 导出必要的接口
4. 编译并放置到hooks目录

### 检测插件开发
1. 实现IDetectionPlugin接口
2. 注册插件到检测引擎
3. 配置插件参数
```

## 许可证和法律声明

### 使用许可
本项目采用MIT许可证，允许自由使用、修改和分发。但请注意：

1. **合法使用**：本工具仅用于学习、研究和测试目的
2. **禁止滥用**：不得用于破解商业软件的授权机制
3. **责任声明**：使用者需自行承担使用风险和法律责任
4. **商业使用**：商业使用前请咨询法律顾问

### 免责声明
- 本软件按"现状"提供，不提供任何明示或暗示的保证
- 开发者不对使用本软件造成的任何损失承担责任
- 用户应遵守当地法律法规和软件许可协议
- 本软件不得用于任何非法目的

### 第三方组件许可
本项目使用了以下第三方组件：
- Microsoft Detours (MIT License)
- SQLite (Public Domain)
- nlohmann/json (MIT License)
- 其他依赖库的许可证请参见相应的LICENSE文件

## 技术支持和社区

### 官方渠道
- **项目主页**: https://github.com/username/universal-multi-launcher
- **问题反馈**: https://github.com/username/universal-multi-launcher/issues
- **技术文档**: https://github.com/username/universal-multi-launcher/wiki
- **更新日志**: https://github.com/username/universal-multi-launcher/releases

### 社区支持
- **QQ群**: 123456789
- **微信群**: 扫描二维码加入
- **论坛**: https://forum.example.com/multilauncher
- **邮件列表**: <EMAIL>

### 贡献指南
欢迎提交代码贡献：
1. Fork项目到您的GitHub账户
2. 创建功能分支进行开发
3. 提交Pull Request
4. 等待代码审查和合并

### 开发路线图
- **v1.1**: 增加更多商业软件支持
- **v1.2**: 图形界面优化和主题支持
- **v1.3**: 机器学习算法改进
- **v2.0**: 跨平台支持（Linux、macOS）

---

**最后更新**: 2023年12月1日
**文档版本**: 1.0.0
**适用程序版本**: 1.0.0及以上
