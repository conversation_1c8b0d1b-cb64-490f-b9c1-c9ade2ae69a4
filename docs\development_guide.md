# 万能多开器开发文档

## 项目概述

万能多开器是一个基于C++和Win32 API开发的高级工具，用于自动识别和绕过各种软件的多开限制机制。支持Win32/Win64应用程序，兼容Windows 7-11系统，主要用于测试自己开发的软件的多开防护效果。

### 核心特性
- **通用兼容性** - 支持.NET Framework、Qt、MFC、WinUI等框架
- **智能检测** - 基于机器学习的限制类型识别
- **用户友好** - 现代化GUI界面，支持拖拽操作
- **高级隐蔽** - 反检测技术，绕过各种保护机制
- **高性能** - 低内存占用，快速响应

## 技术架构

### 完整模块设计

```
UniversalMultiLauncher/
├── src/
│   ├── core/                              # 核心引擎
│   │   ├── UniversalMultiLauncher.h/cpp    # 主控制器
│   │   ├── RestrictionDetector.h/cpp       # 智能检测器
│   │   ├── BypassEngine.h/cpp              # 绕过引擎
│   │   ├── MLClassifier.h/cpp              # 机器学习分类器
│   │   └── RuleEngine.h/cpp                # 规则引擎
│   ├── gui/                               # 图形界面
│   │   ├── MainWindow.h/cpp                # 主窗口
│   │   ├── ProgressDialog.h/cpp            # 进度对话框
│   │   ├── LogViewer.h/cpp                 # 日志查看器
│   │   ├── SettingsDialog.h/cpp            # 设置对话框
│   │   └── DragDropHandler.h/cpp           # 拖拽处理器
│   ├── hooks/                             # Hook模块
│   │   ├── mutex_hook.cpp                  # Mutex Hook DLL
│   │   ├── file_hook.cpp                   # 文件 Hook DLL
│   │   ├── registry_hook.cpp               # 注册表 Hook DLL
│   │   ├── window_hook.cpp                 # 窗口 Hook DLL
│   │   ├── process_hook.cpp                # 进程 Hook DLL
│   │   ├── memory_hook.cpp                 # 内存 Hook DLL
│   │   └── stealth_hook.cpp                # 隐蔽 Hook DLL
│   ├── utils/                             # 工具模块
│   │   ├── PEAnalyzer.h/cpp               # PE文件分析
│   │   ├── ProcessInjector.h/cpp          # 进程注入
│   │   ├── APIMonitor.h/cpp               # API监控
│   │   ├── AntiDebug.h/cpp                # 反调试检测
│   │   ├── VMDetector.h/cpp               # 虚拟机检测
│   │   ├── MemoryManager.h/cpp            # 内存管理
│   │   └── CryptoUtils.h/cpp              # 加密工具
│   ├── detection/                         # 检测模块
│   │   ├── StaticAnalyzer.h/cpp           # 静态分析
│   │   ├── DynamicAnalyzer.h/cpp          # 动态分析
│   │   ├── BehaviorAnalyzer.h/cpp         # 行为分析
│   │   ├── SignatureDB.h/cpp              # 特征数据库
│   │   └── ConfidenceEvaluator.h/cpp      # 置信度评估
│   ├── bypass/                            # 绕过策略
│   │   ├── MutexBypass.h/cpp              # Mutex绕过
│   │   ├── FileBypass.h/cpp               # 文件绕过
│   │   ├── RegistryBypass.h/cpp           # 注册表绕过
│   │   ├── ProcessBypass.h/cpp            # 进程绕过
│   │   ├── WindowBypass.h/cpp             # 窗口绕过
│   │   └── NetworkBypass.h/cpp            # 网络绕过
│   ├── stealth/                           # 隐蔽技术
│   │   ├── DLLHiding.h/cpp                # DLL隐藏
│   │   ├── HookChainHiding.h/cpp          # Hook链隐藏
│   │   ├── MemoryCleaner.h/cpp            # 内存清理
│   │   ├── AntiAntiDebug.h/cpp            # 反反调试
│   │   └── VMEvasion.h/cpp                # 虚拟机逃逸
│   └── main.cpp                           # 程序入口
├── include/                               # 头文件
├── libs/                                  # 第三方库
│   ├── detours/                           # Microsoft Detours
│   ├── json/                              # JSON解析库
│   ├── sqlite/                            # SQLite数据库
│   └── ml/                                # 机器学习库
├── resources/                             # 资源文件
│   ├── icons/                             # 图标
│   ├── configs/                           # 配置文件
│   └── signatures/                        # 特征库
├── docs/                                  # 文档
├── tests/                                 # 测试程序
└── build/                                 # 编译输出
```

### 类关系图

```
UniversalMultiLauncher (主控制器)
├── MainWindow (GUI主窗口)
│   ├── DragDropHandler (拖拽处理)
│   ├── ProgressDialog (进度显示)
│   └── LogViewer (日志查看)
├── RestrictionDetector (检测器)
│   ├── StaticAnalyzer (静态分析)
│   ├── DynamicAnalyzer (动态分析)
│   ├── MLClassifier (机器学习)
│   └── ConfidenceEvaluator (置信度)
├── BypassEngine (绕过引擎)
│   ├── MutexBypass (Mutex绕过)
│   ├── FileBypass (文件绕过)
│   ├── RegistryBypass (注册表绕过)
│   └── ProcessBypass (进程绕过)
└── StealthManager (隐蔽管理)
    ├── DLLHiding (DLL隐藏)
    ├── MemoryCleaner (内存清理)
    └── AntiAntiDebug (反反调试)
```

## 开发环境配置

### 必需工具

1. **Visual Studio 2019/2022** 或 **MinGW-w64**
2. **Windows SDK 10.0** 或更高版本
3. **Microsoft Detours 4.0.1** - API Hook库
4. **SQLite 3.x** - 数据库支持
5. **nlohmann/json** - JSON解析
6. **Git** - 版本控制

### 依赖库安装

#### 1. Microsoft Detours
```bash
# 下载并编译Detours
git clone https://github.com/Microsoft/Detours.git
cd Detours
nmake
```

#### 2. SQLite集成
```bash
# 下载SQLite源码
wget https://www.sqlite.org/2023/sqlite-amalgamation-3420000.zip
unzip sqlite-amalgamation-3420000.zip
```

#### 3. JSON库集成
```bash
# 使用vcpkg安装
vcpkg install nlohmann-json:x64-windows
```

#### 4. 完整项目配置
```cmake
# CMakeLists.txt
cmake_minimum_required(VERSION 3.16)
project(UniversalMultiLauncher)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 包含目录
include_directories(include)
include_directories(libs/detours/include)
include_directories(libs/sqlite)
include_directories(libs/json/include)

# 链接库目录
link_directories(libs/detours/lib.X64)

# 主程序源文件
set(CORE_SOURCES
    src/main.cpp
    src/core/UniversalMultiLauncher.cpp
    src/core/RestrictionDetector.cpp
    src/core/BypassEngine.cpp
    src/core/MLClassifier.cpp
    src/core/RuleEngine.cpp
)

set(GUI_SOURCES
    src/gui/MainWindow.cpp
    src/gui/ProgressDialog.cpp
    src/gui/LogViewer.cpp
    src/gui/SettingsDialog.cpp
    src/gui/DragDropHandler.cpp
)

set(UTILS_SOURCES
    src/utils/PEAnalyzer.cpp
    src/utils/ProcessInjector.cpp
    src/utils/APIMonitor.cpp
    src/utils/AntiDebug.cpp
    src/utils/VMDetector.cpp
    src/utils/MemoryManager.cpp
    src/utils/CryptoUtils.cpp
)

set(DETECTION_SOURCES
    src/detection/StaticAnalyzer.cpp
    src/detection/DynamicAnalyzer.cpp
    src/detection/BehaviorAnalyzer.cpp
    src/detection/SignatureDB.cpp
    src/detection/ConfidenceEvaluator.cpp
)

set(BYPASS_SOURCES
    src/bypass/MutexBypass.cpp
    src/bypass/FileBypass.cpp
    src/bypass/RegistryBypass.cpp
    src/bypass/ProcessBypass.cpp
    src/bypass/WindowBypass.cpp
    src/bypass/NetworkBypass.cpp
)

set(STEALTH_SOURCES
    src/stealth/DLLHiding.cpp
    src/stealth/HookChainHiding.cpp
    src/stealth/MemoryCleaner.cpp
    src/stealth/AntiAntiDebug.cpp
    src/stealth/VMEvasion.cpp
)

# SQLite源文件
set(SQLITE_SOURCES
    libs/sqlite/sqlite3.c
)

# 主可执行文件
add_executable(MultiLauncher
    ${CORE_SOURCES}
    ${GUI_SOURCES}
    ${UTILS_SOURCES}
    ${DETECTION_SOURCES}
    ${BYPASS_SOURCES}
    ${STEALTH_SOURCES}
    ${SQLITE_SOURCES}
)

# 链接库
target_link_libraries(MultiLauncher
    detours
    psapi
    advapi32
    kernel32
    user32
    shell32
    ole32
    oleaut32
    uuid
    comctl32
    gdi32
    comdlg32
    shlwapi
    wininet
    ws2_32
    dbghelp
    version
)

# Hook DLL列表
set(HOOK_DLLS
    mutex_hook
    file_hook
    registry_hook
    window_hook
    process_hook
    memory_hook
    stealth_hook
)

# 编译所有Hook DLL
foreach(HOOK_DLL ${HOOK_DLLS})
    add_library(${HOOK_DLL} SHARED src/hooks/${HOOK_DLL}.cpp)
    target_link_libraries(${HOOK_DLL} detours kernel32 user32 advapi32)
    set_target_properties(${HOOK_DLL} PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/hooks
    )
endforeach()

# 资源文件
if(WIN32)
    target_sources(MultiLauncher PRIVATE resources/app.rc)
endif()

# 编译选项
if(MSVC)
    target_compile_options(MultiLauncher PRIVATE /W4 /WX)
else()
    target_compile_options(MultiLauncher PRIVATE -Wall -Wextra -Werror)
endif()

# 调试信息
set_target_properties(MultiLauncher PROPERTIES
    DEBUG_POSTFIX "_d"
    RELEASE_POSTFIX ""
)
```

## 用户友好的GUI界面设计

### 主窗口实现

```cpp
#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <windows.h>
#include <commctrl.h>
#include <commdlg.h>
#include <shellapi.h>
#include <string>
#include <vector>
#include <memory>

class DragDropHandler;
class ProgressDialog;
class LogViewer;

class MainWindow {
private:
    HWND hWnd;                          // 主窗口句柄
    HWND hFileEdit;                     // 文件路径编辑框
    HWND hBrowseBtn;                    // 浏览按钮
    HWND hInstanceSpin;                 // 实例数量调节器
    HWND hLaunchBtn;                    // 启动按钮
    HWND hAnalyzeBtn;                   // 分析按钮
    HWND hStatusBar;                    // 状态栏
    HWND hProgressBar;                  // 进度条
    HWND hLogList;                      // 日志列表
    HWND hDetectionList;                // 检测结果列表
    
    std::unique_ptr<DragDropHandler> dragDropHandler;
    std::unique_ptr<ProgressDialog> progressDialog;
    std::unique_ptr<LogViewer> logViewer;
    
    HFONT hFont;                        // 界面字体
    HICON hIcon;                        // 应用图标
    
    bool isAnalyzing;                   // 分析状态标志
    bool isLaunching;                   // 启动状态标志
    
public:
    MainWindow();
    ~MainWindow();
    
    bool Initialize(HINSTANCE hInstance);
    void Show(int nCmdShow);
    void UpdateStatus(const std::string& message);
    void UpdateProgress(int percentage);
    void AddLogEntry(const std::string& message, int level = 0);
    void UpdateDetectionResults(const std::vector<std::string>& results);
    
    // 事件处理
    void OnBrowseFile();
    void OnDragDrop(const std::vector<std::string>& files);
    void OnAnalyze();
    void OnLaunch();
    void OnSettings();
    void OnAbout();
    
    static LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
    
private:
    void CreateControls();
    void SetupLayout();
    void SetupDragDrop();
    void LoadSettings();
    void SaveSettings();
    bool ValidateInput();
    void EnableControls(bool enabled);
};

#endif
```

## 核心功能实现

### 1. 限制检测机制

#### 支持的限制类型
- **Mutex互斥体** - `CreateMutex`, `OpenMutex`
- **文件锁定** - 独占文件访问
- **注册表锁定** - 注册表键值检测
- **窗口类检测** - `FindWindow`, `EnumWindows`
- **进程名检测** - `CreateToolhelp32Snapshot`
- **共享内存** - `CreateFileMapping`
- **命名管道** - `CreateNamedPipe`
- **硬件ID检测** - 硬件指纹
- **网络端口** - 端口占用检测

#### 检测流程
```cpp
DetectionResult AnalyzeApplication(const std::string& exePath) {
    // 1. 静态分析 - PE导入表扫描
    // 2. 动态分析 - 运行时API监控
    // 3. 内存扫描 - 特征字符串查找
    // 4. 行为分析 - 系统调用模式
    // 5. 数据库匹配 - 已知应用特征
}
```

### 2. 绕过策略

#### Mutex绕过
```cpp
// 策略1: API Hook - 修改Mutex名称
HANDLE HookedCreateMutex(LPCSTR lpName) {
    string newName = string(lpName) + "_" + to_string(GetCurrentProcessId());
    return TrueCreateMutex(newName.c_str());
}

// 策略2: 内存补丁 - 直接修改内存中的Mutex名
bool PatchMutexInMemory(HANDLE hProcess, DWORD address);

// 策略3: 进程隔离 - 独立的对象命名空间
bool CreateIsolatedNamespace(DWORD processId);
```

#### 文件锁定绕过
```cpp
// 策略1: 文件重定向
bool SetupFileRedirection(const string& originalPath, const string& redirectPath);

// 策略2: 虚拟文件系统
bool CreateVirtualFileSystem(DWORD processId);

// 策略3: 文件句柄复制
bool DuplicateFileHandles(HANDLE hSourceProcess, HANDLE hTargetProcess);
```

#### 注册表绕过
```cpp
// 策略1: 注册表虚拟化
bool CreateVirtualRegistry(const string& basePath);

// 策略2: API重定向
LONG HookedRegOpenKey(HKEY hKey, LPCSTR lpSubKey, PHKEY phkResult);

// 策略3: 注册表快照
bool CreateRegistrySnapshot(const string& keyPath);
```

### 3. 进程注入技术

#### DLL注入方法
```cpp
class ProcessInjector {
public:
    // 方法1: CreateRemoteThread + LoadLibrary
    bool InjectDLL_CreateRemoteThread(DWORD processId, const string& dllPath);
    
    // 方法2: Manual DLL Mapping
    bool InjectDLL_ManualMapping(DWORD processId, const string& dllPath);
    
    // 方法3: SetWindowsHookEx
    bool InjectDLL_SetHook(DWORD processId, const string& dllPath);
    
    // 方法4: NtCreateThreadEx
    bool InjectDLL_NtCreateThread(DWORD processId, const string& dllPath);
};
```

## API参考

### 主要类接口

#### UniversalMultiLauncher
```cpp
class UniversalMultiLauncher {
public:
    bool Initialize();
    DetectionResult AnalyzeApplication(const string& exePath);
    bool LaunchWithBypass(const string& exePath, int instanceCount = 1);
    bool SaveAnalysisResult(const string& exePath, const DetectionResult& result);
    bool LoadKnownApps();
    
private:
    map<string, DetectionResult> appDatabase;
    vector<BypassStrategy> strategies;
};
```

#### RestrictionDetector
```cpp
class RestrictionDetector {
public:
    DetectionResult AnalyzeApp(const string& exePath);
    bool AnalyzePEImports(const string& exePath, DetectionResult& result);
    bool MonitorRuntimeBehavior(const string& exePath, DetectionResult& result);
    bool ScanMemoryPatterns(DetectionResult& result);
    
private:
    bool FindAPIInImports(const string& exePath, const string& apiName);
    void ClassifyRestrictionType(const string& apiName, DetectionResult& result);
};
```

#### BypassEngine
```cpp
class BypassEngine {
public:
    bool ApplyBypass(const string& exePath, const DetectionResult& detection);
    bool BypassMutex(const string& exePath, const DetectionResult& detection);
    bool BypassFileLock(const string& exePath, const DetectionResult& detection);
    bool BypassRegistryLock(const string& exePath, const DetectionResult& detection);
    
private:
    bool InjectHookDLL(const string& exePath, const string& dllPath, const string& hookName);
    string CreateInstanceDirectory(const string& exePath);
    string CreateVirtualRegistryBranch();
};
```

## 编译指南

### Visual Studio编译
```batch
# 1. 打开Developer Command Prompt
# 2. 编译主程序
cl /EHsc /I"include" /I"libs/detours/include" src/main.cpp src/core/*.cpp src/utils/*.cpp /link libs/detours/lib.X64/detours.lib psapi.lib advapi32.lib kernel32.lib user32.lib /OUT:MultiLauncher.exe

# 3. 编译Hook DLL
cl /LD /EHsc /I"libs/detours/include" src/hooks/mutex_hook.cpp /link libs/detours/lib.X64/detours.lib /OUT:mutex_hook.dll
cl /LD /EHsc /I"libs/detours/include" src/hooks/file_hook.cpp /link libs/detours/lib.X64/detours.lib /OUT:file_hook.dll
cl /LD /EHsc /I"libs/detours/include" src/hooks/registry_hook.cpp /link libs/detours/lib.X64/detours.lib /OUT:registry_hook.dll
cl /LD /EHsc /I"libs/detours/include" src/hooks/window_hook.cpp /link libs/detours/lib.X64/detours.lib /OUT:window_hook.dll
```

### CMake编译
```batch
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

### MinGW编译
```batch
g++ -std=c++17 -O2 -I"include" -I"libs/detours/include" src/main.cpp src/core/*.cpp src/utils/*.cpp -L"libs/detours/lib" -ldetours -lpsapi -ladvapi32 -lkernel32 -luser32 -lshlwapi -o MultiLauncher.exe

g++ -shared -std=c++17 -O2 -I"libs/detours/include" src/hooks/mutex_hook.cpp -L"libs/detours/lib" -ldetours -o mutex_hook.dll
```

## 测试指南

### 单元测试
```cpp
// tests/test_detector.cpp
#include "gtest/gtest.h"
#include "RestrictionDetector.h"

TEST(RestrictionDetectorTest, DetectMutex) {
    RestrictionDetector detector;
    DetectionResult result = detector.AnalyzeApp("test_apps/mutex_app.exe");
    
    EXPECT_TRUE(find(result.restrictions.begin(), result.restrictions.end(), 
                    RestrictionType::MUTEX_LOCK) != result.restrictions.end());
}

TEST(RestrictionDetectorTest, DetectFileLock) {
    RestrictionDetector detector;
    DetectionResult result = detector.AnalyzeApp("test_apps/file_lock_app.exe");
    
    EXPECT_TRUE(find(result.restrictions.begin(), result.restrictions.end(), 
                    RestrictionType::FILE_LOCK) != result.restrictions.end());
}
```

### 集成测试
```cpp
// tests/test_integration.cpp
TEST(IntegrationTest, LaunchMultipleInstances) {
    UniversalMultiLauncher launcher;
    ASSERT_TRUE(launcher.Initialize());
    
    string testApp = "test_apps/sample_app.exe";
    EXPECT_TRUE(launcher.LaunchWithBypass(testApp, 3));
    
    // 验证确实启动了3个实例
    int instanceCount = CountProcessInstances("sample_app.exe");
    EXPECT_EQ(instanceCount, 3);
}
```

### 测试应用程序
创建简单的测试程序来验证各种限制机制：

```cpp
// test_apps/mutex_test.cpp - Mutex限制测试
int main() {
    HANDLE hMutex = CreateMutexA(NULL, TRUE, "TestAppMutex");
    if (GetLastError() == ERROR_ALREADY_EXISTS) {
        MessageBoxA(NULL, "应用程序已在运行", "错误", MB_OK);
        return 1;
    }
    
    MessageBoxA(NULL, "应用程序启动成功", "信息", MB_OK);
    CloseHandle(hMutex);
    return 0;
}
```

## 使用说明

### 命令行用法
```batch
# 基本用法
MultiLauncher.exe "C:\Program Files\TestApp\app.exe"

# 启动多个实例
MultiLauncher.exe "C:\Program Files\TestApp\app.exe" 3

# 分析模式（仅分析不启动）
MultiLauncher.exe --analyze "C:\Program Files\TestApp\app.exe"

# 详细输出
MultiLauncher.exe --verbose "C:\Program Files\TestApp\app.exe"

# 保存分析结果
MultiLauncher.exe --save-profile "C:\Program Files\TestApp\app.exe"
```

### 配置文件
```json
// config/app_profiles.json
{
  "profiles": [
    {
      "name": "TestApp",
      "executable": "testapp.exe",
      "restrictions": ["MUTEX_LOCK", "FILE_LOCK"],
      "bypass_strategies": [
        {
          "type": "MUTEX_LOCK",
          "method": "API_HOOK",
          "dll": "mutex_hook.dll"
        },
        {
          "type": "FILE_LOCK",
          "method": "FILE_REDIRECT",
          "target_dir": "%TEMP%\\MultiLauncher\\{PID}"
        }
      ]
    }
  ]
}
```

## 调试指南

### 调试工具
1. **Process Monitor** - 监控文件/注册表访问
2. **API Monitor** - 监控API调用
3. **Dependency Walker** - 分析DLL依赖
4. **OllyDbg/x64dbg** - 动态调试
5. **Visual Studio Debugger** - 源码调试

### 常见问题

#### 1. DLL注入失败
```cpp
// 检查目标进程架构
bool IsProcess64Bit(DWORD processId) {
    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, processId);
    BOOL isWow64 = FALSE;
    IsWow64Process(hProcess, &isWow64);
    CloseHandle(hProcess);
    return !isWow64;
}
```

#### 2. Hook失败
```cpp
// 验证API地址
FARPROC apiAddr = GetProcAddress(GetModuleHandle(L"kernel32.dll"), "CreateMutexA");
if (apiAddr == nullptr) {
    // API不存在或已被Hook
}
```

#### 3. 权限不足
```cpp
// 提升权限
bool EnableDebugPrivilege() {
    HANDLE hToken;
    TOKEN_PRIVILEGES tp;
    
    if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES, &hToken))
        return false;
        
    tp.PrivilegeCount = 1;
    LookupPrivilegeValue(NULL, SE_DEBUG_NAME, &tp.Privileges[0].Luid);
    tp.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;
    
    AdjustTokenPrivileges(hToken, FALSE, &tp, sizeof(tp), NULL, NULL);
    CloseHandle(hToken);
    
    return GetLastError() == ERROR_SUCCESS;
}
```

## 性能优化

### 1. 检测优化
- 缓存PE分析结果
- 并行化检测过程
- 智能跳过已知安全的API

### 2. 注入优化
- 预编译Hook DLL
- 使用内存池管理
- 延迟加载非关键模块

### 3. 内存优化
- 及时释放不需要的资源
- 使用智能指针管理内存
- 避免内存泄漏

## 安全考虑

### 1. 代码签名
```batch
# 对编译后的文件进行签名
signtool sign /f certificate.pfx /p password /t http://timestamp.server.com MultiLauncher.exe
```

### 2. 反病毒误报
- 使用白名单技术
- 提交样本到杀毒厂商
- 添加数字签名

### 3. 权限控制
- 最小权限原则
- 用户确认机制
- 审计日志记录

## 扩展开发

### 添加新的检测类型
```cpp
// 1. 在RestrictionType枚举中添加新类型
enum class RestrictionType {
    // ... 现有类型
    NEW_RESTRICTION_TYPE
};

// 2. 在RestrictionDetector中添加检测逻辑
bool DetectNewRestriction(const string& exePath, DetectionResult& result);

// 3. 在BypassEngine中添加绕过策略
bool BypassNewRestriction(const string& exePath, const DetectionResult& detection);
```

### 添加新的绕过方法
```cpp
// 1. 创建新的Hook DLL
// src/hooks/new_hook.cpp

// 2. 在BypassEngine中注册新方法
bool RegisterNewBypassMethod(const string& methodName, BypassFunction func);

// 3. 更新配置文件格式
```

## 许可证

本项目仅用于学习和研究目的。请遵守相关法律法规，不得用于破解商业软件的授权机制。

## 联系方式

- 项目主页: https://github.com/username/universal-multi-launcher
- 问题反馈: https://github.com/username/universal-multi-launcher/issues
- 邮箱: <EMAIL>
